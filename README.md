# NEXUS - 创业者社区平台

## 项目简介
一个基于 HTML、CSS、JS、PHP 和 MySQL 的创业者社区平台。创业者可以在这里发现志同道合的伙伴，展示自己的主业和副业，互相关注获取联系方式，分享行业经验与资源，通过弹幕功能互相激励。

## 🚀 主要功能

### 创业者社区
- **创业者展示**: 展示个人信息、主业、副业和标签
- **关注系统**: 互相关注后可查看联系方式
- **在线状态**: 实时显示用户在线状态

### 社区动态
- **经验分享**: 分享创业经验和行业见解
- **踩坑分享**: 帮助其他创业者避免常见错误
- **互动功能**: 点赞、评论、分享功能

### 资源合作
- **资源发布**: 发布合作需求或提供资源
- **紧急程度**: 标记需求的紧急程度
- **分类管理**: 按类型组织资源信息

### 弹幕激励
- **实时弹幕**: 发送激励弹幕互相鼓励
- **动态效果**: 流畅的弹幕动画效果
- **互动交流**: 创业者之间的轻松交流方式

## 技术栈
- 前端：HTML5、CSS3、JavaScript (ES6+)
- 后端：PHP 7.4+
- 数据库：MySQL 8.0+
- 服务器：Apache/Nginx

## 📁 项目结构
```
entrepreneurship/
├── index.html                    # 主页入口
├── frontend/                     # 前端代码
│   ├── shared/                   # 共享资源
│   │   ├── css/main-theme.css   # 主样式文件
│   │   └── js/main-theme.js     # 主题JavaScript
│   └── modules/                  # 功能模块
│       ├── entrepreneurship/     # 创业模块
│       │   ├── pages/           # 页面文件
│       │   ├── css/             # 模块样式
│       │   └── js/              # 模块脚本
│       └── dating/              # 相亲模块（待开发）
├── backend/                      # 后端代码
├── database/                     # 数据库相关
├── config/                       # 配置文件
├── uploads/                      # 上传文件存储
├── logs/                         # 日志文件
├── docs/                         # 项目文档
└── scripts/                      # 部署和维护脚本
```

## 🎨 设计特色
- **现代高级设计**: 采用毛玻璃效果、渐变色彩和流畅动画
- **模块化架构**: 每个功能模块独立开发，便于维护和扩展
- **响应式设计**: 适配各种设备屏幕尺寸
- **高级交互**: 丰富的hover效果和过渡动画

## 🚀 快速开始

### 本地开发
1. 克隆项目到本地
2. 在浏览器中打开 `index.html`
3. 点击"进入创业社区"开始体验

### 功能导航
- **创业者**: 浏览和关注其他创业者
- **社区动态**: 查看和发布创业相关内容
- **资源合作**: 寻找合作伙伴和资源
- **弹幕激励**: 发送激励弹幕互相鼓励

## 📋 部署说明
1. 将项目上传到服务器 `/www/wwwroot/CY` 目录
2. 配置数据库连接
3. 设置文件权限
4. 配置域名解析到 `lllll.club`

## 💻 开发规范
- 遵循 PSR-4 自动加载规范
- 使用 MVC 架构模式
- 前端采用模块化开发
- 数据库操作使用 PDO
- 遵循 KISS 原则和 SOLID 原则
- 保持代码风格一致，单一职责原则

## 🔮 未来规划
- [ ] 用户注册和登录系统
- [ ] 私信功能
- [ ] 项目展示功能
- [ ] 相亲交友模块
- [ ] 移动端APP
- [ ] 实时通知系统
