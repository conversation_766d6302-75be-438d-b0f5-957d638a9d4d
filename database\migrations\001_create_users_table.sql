-- 创建用户表
-- 创业者社区平台用户表

CREATE TABLE IF NOT EXISTS `cy_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名/账号',
  `nickname` varchar(100) NOT NULL COMMENT '昵称',
  `password` varchar(255) NOT NULL COMMENT '密码哈希',
  `bio` varchar(255) DEFAULT NULL COMMENT '个人备注/职业描述',
  `main_business` varchar(255) DEFAULT NULL COMMENT '主业',
  `side_business` varchar(255) DEFAULT NULL COMMENT '副业',
  `contact_type` enum('phone','qq','wechat') DEFAULT NULL COMMENT '联系方式类型',
  `contact_value` varchar(100) DEFAULT NULL COMMENT '联系方式值',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `location` varchar(100) DEFAULT NULL COMMENT '所在地',
  `status` enum('online','away','offline') DEFAULT 'offline' COMMENT '在线状态',
  `followers_count` int(11) DEFAULT 0 COMMENT '关注者数量',
  `following_count` int(11) DEFAULT 0 COMMENT '关注数量',
  `posts_count` int(11) DEFAULT 0 COMMENT '动态数量',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  `email_verified_at` timestamp NULL DEFAULT NULL COMMENT '邮箱验证时间',
  `remember_token` varchar(100) DEFAULT NULL COMMENT '记住登录令牌',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_nickname` (`nickname`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建用户标签表
CREATE TABLE IF NOT EXISTS `cy_user_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tag_name` (`tag_name`),
  FOREIGN KEY (`user_id`) REFERENCES `cy_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户标签表';

-- 创建用户关注关系表
CREATE TABLE IF NOT EXISTS `cy_user_follows` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关注ID',
  `follower_id` int(11) NOT NULL COMMENT '关注者ID',
  `following_id` int(11) NOT NULL COMMENT '被关注者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_follow` (`follower_id`, `following_id`),
  KEY `idx_follower` (`follower_id`),
  KEY `idx_following` (`following_id`),
  FOREIGN KEY (`follower_id`) REFERENCES `cy_users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`following_id`) REFERENCES `cy_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户关注关系表';

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS `cy_user_sessions` (
  `id` varchar(255) NOT NULL COMMENT '会话ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `payload` longtext NOT NULL COMMENT '会话数据',
  `last_activity` int(11) NOT NULL COMMENT '最后活动时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';
