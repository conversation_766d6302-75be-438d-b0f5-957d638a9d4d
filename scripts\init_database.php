<?php
/**
 * 数据库初始化脚本
 * 创业者社区平台
 */

require_once __DIR__ . '/../backend/utils/Database.php';

try {
    echo "开始初始化数据库...\n";
    
    $db = Database::getInstance();
    $connection = $db->getConnection();
    
    // 读取SQL文件
    $sqlFile = __DIR__ . '/../database/migrations/001_create_users_table.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: {$sqlFile}");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // 分割SQL语句（按分号分割）
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    // 执行每个SQL语句
    foreach ($statements as $statement) {
        if (trim($statement)) {
            echo "执行SQL: " . substr($statement, 0, 50) . "...\n";
            $connection->exec($statement);
        }
    }
    
    echo "数据库初始化完成！\n";
    echo "创建的表:\n";
    echo "- cy_users (用户表)\n";
    echo "- cy_user_tags (用户标签表)\n";
    echo "- cy_user_follows (用户关注关系表)\n";
    echo "- cy_user_sessions (用户会话表)\n";
    
} catch (Exception $e) {
    echo "数据库初始化失败: " . $e->getMessage() . "\n";
    exit(1);
}
