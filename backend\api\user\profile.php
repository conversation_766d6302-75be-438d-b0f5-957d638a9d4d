<?php
/**
 * 获取用户个人信息API
 * 创业者社区平台
 */

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许GET请求'
    ]);
    exit();
}

require_once __DIR__ . '/../../models/user/User.php';

try {
    // 获取当前用户ID
    $currentUserId = getCurrentUserId();
    if (!$currentUserId) {
        throw new Exception('请先登录');
    }

    $userModel = new User();
    
    // 获取用户信息
    $user = $userModel->findById($currentUserId);
    if (!$user) {
        throw new Exception('用户不存在');
    }

    // 返回用户信息（不包含密码）
    $userData = [
        'id' => $user['id'],
        'username' => $user['username'],
        'nickname' => $user['nickname'],
        'bio' => $user['bio'],
        'main_business' => $user['main_business'],
        'side_business' => $user['side_business'],
        'location' => $user['location'],
        'description' => $user['description'],
        'contact_type' => $user['contact_type'],
        'contact_value' => $user['contact_value'],
        'status' => $user['status'],
        'followers_count' => $user['followers_count'],
        'following_count' => $user['following_count'],
        'posts_count' => $user['posts_count'],
        'is_visible' => $user['is_visible'],
        'created_at' => $user['created_at'],
        'updated_at' => $user['updated_at']
    ];

    echo json_encode([
        'success' => true,
        'message' => '获取用户信息成功',
        'data' => $userData
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 获取当前用户ID
 */
function getCurrentUserId()
{
    // 从Authorization头获取token
    $headers = getallheaders();
    if (!isset($headers['Authorization'])) {
        return null;
    }

    $token = str_replace('Bearer ', '', $headers['Authorization']);
    
    try {
        $payload = json_decode(base64_decode($token), true);
        if (!$payload || !isset($payload['user_id'])) {
            return null;
        }

        // 检查token是否过期
        if (time() - $payload['timestamp'] > 24 * 60 * 60) {
            return null;
        }

        return $payload['user_id'];
    } catch (Exception $e) {
        return null;
    }
}
?>
