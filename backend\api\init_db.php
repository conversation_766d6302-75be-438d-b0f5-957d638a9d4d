<?php
/**
 * 数据库初始化API
 * 创业者社区平台
 */

header('Content-Type: application/json; charset=utf-8');

try {
    // 直接连接数据库
    $config = require __DIR__ . '/../../config/database/database.php';
    
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);

    // 创建用户表
    $createUsersTable = "
    CREATE TABLE IF NOT EXISTS `cy_users` (
      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
      `username` varchar(50) NOT NULL COMMENT '用户名/账号',
      `nickname` varchar(100) NOT NULL COMMENT '昵称',
      `password` varchar(255) NOT NULL COMMENT '密码哈希',
      `bio` varchar(255) DEFAULT NULL COMMENT '个人备注/职业描述',
      `main_business` varchar(255) DEFAULT NULL COMMENT '主业',
      `side_business` varchar(255) DEFAULT NULL COMMENT '副业',
      `contact_type` enum('phone','qq','wechat') DEFAULT NULL COMMENT '联系方式类型',
      `contact_value` varchar(100) DEFAULT NULL COMMENT '联系方式值',
      `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
      `location` varchar(100) DEFAULT NULL COMMENT '所在地',
      `status` enum('online','away','offline') DEFAULT 'offline' COMMENT '在线状态',
      `followers_count` int(11) DEFAULT 0 COMMENT '关注者数量',
      `following_count` int(11) DEFAULT 0 COMMENT '关注数量',
      `posts_count` int(11) DEFAULT 0 COMMENT '动态数量',
      `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
      `is_visible` tinyint(1) DEFAULT 1 COMMENT '是否在创业者列表中可见',
      `email_verified_at` timestamp NULL DEFAULT NULL COMMENT '邮箱验证时间',
      `remember_token` varchar(100) DEFAULT NULL COMMENT '记住登录令牌',
      `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
      `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (`id`),
      UNIQUE KEY `username` (`username`),
      KEY `idx_nickname` (`nickname`),
      KEY `idx_created_at` (`created_at`),
      KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表'
    ";

    // 创建用户标签表
    $createUserTagsTable = "
    CREATE TABLE IF NOT EXISTS `cy_user_tags` (
      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
      `user_id` int(11) NOT NULL COMMENT '用户ID',
      `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      PRIMARY KEY (`id`),
      KEY `idx_user_id` (`user_id`),
      KEY `idx_tag_name` (`tag_name`),
      FOREIGN KEY (`user_id`) REFERENCES `cy_users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户标签表'
    ";

    // 创建用户关注关系表
    $createUserFollowsTable = "
    CREATE TABLE IF NOT EXISTS `cy_user_follows` (
      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关注ID',
      `follower_id` int(11) NOT NULL COMMENT '关注者ID',
      `following_id` int(11) NOT NULL COMMENT '被关注者ID',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关注时间',
      PRIMARY KEY (`id`),
      UNIQUE KEY `unique_follow` (`follower_id`, `following_id`),
      KEY `idx_follower` (`follower_id`),
      KEY `idx_following` (`following_id`),
      FOREIGN KEY (`follower_id`) REFERENCES `cy_users` (`id`) ON DELETE CASCADE,
      FOREIGN KEY (`following_id`) REFERENCES `cy_users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户关注关系表'
    ";

    // 执行SQL语句
    $pdo->exec($createUsersTable);
    $pdo->exec($createUserTagsTable);
    $pdo->exec($createUserFollowsTable);

    // 检查并添加is_visible字段（如果不存在）
    try {
        $pdo->exec("ALTER TABLE cy_users ADD COLUMN is_visible tinyint(1) DEFAULT 1 COMMENT '是否在创业者列表中可见'");
    } catch (Exception $e) {
        // 字段可能已存在，忽略错误
    }

    echo json_encode([
        'success' => true,
        'message' => '数据库表创建成功',
        'tables' => [
            'cy_users' => '用户表',
            'cy_user_tags' => '用户标签表',
            'cy_user_follows' => '用户关注关系表'
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '数据库初始化失败: ' . $e->getMessage()
    ]);
}
