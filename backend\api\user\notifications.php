<?php
/**
 * 获取用户关注通知API
 * 创业者社区平台
 */

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许GET请求'
    ]);
    exit();
}

require_once __DIR__ . '/../../models/user/User.php';

try {
    // 获取当前用户ID
    $currentUserId = getCurrentUserId();
    if (!$currentUserId) {
        throw new Exception('请先登录');
    }

    $userModel = new User();
    
    // 获取通知列表
    $notifications = $userModel->getFollowNotifications($currentUserId);
    
    // 处理通知数据
    $processedNotifications = [];
    foreach ($notifications as $notification) {
        $message = '';
        if ($notification['type'] === 'follow') {
            $message = $notification['follower_nickname'] . ' 关注了你，回关后可查看联系方式';
        } elseif ($notification['type'] === 'mutual_follow') {
            $message = '你和 ' . $notification['follower_nickname'] . ' 已互相关注，现在可以查看联系方式了';
        }
        
        $processedNotifications[] = [
            'id' => $notification['id'],
            'type' => $notification['type'],
            'follower_id' => $notification['follower_id'],
            'follower_nickname' => $notification['follower_nickname'],
            'message' => $message,
            'is_read' => $notification['is_read'],
            'created_at' => $notification['created_at']
        ];
    }

    echo json_encode([
        'success' => true,
        'message' => '获取通知成功',
        'data' => $processedNotifications
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 获取当前用户ID
 */
function getCurrentUserId()
{
    // 从Authorization头获取token
    $headers = getallheaders();
    if (!isset($headers['Authorization'])) {
        return null;
    }

    $token = str_replace('Bearer ', '', $headers['Authorization']);
    
    try {
        $payload = json_decode(base64_decode($token), true);
        if (!$payload || !isset($payload['user_id'])) {
            return null;
        }

        // 检查token是否过期
        if (time() - $payload['timestamp'] > 24 * 60 * 60) {
            return null;
        }

        return $payload['user_id'];
    } catch (Exception $e) {
        return null;
    }
}
