/* 认证页面专用样式 */

/* 认证容器 */
.auth-container {
    min-height: calc(100vh - 120px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    position: relative;
}

/* 认证卡片 */
.auth-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 24px;
    padding: 40px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    box-shadow: var(--shadow-2xl), 0 0 60px rgba(99, 102, 241, 0.2);
    width: 100%;
    max-width: 480px;
    position: relative;
    z-index: 10;
    animation: authCardSlideIn 0.8s ease-out;
}

.register-card {
    max-width: 600px;
}

@keyframes authCardSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 认证头部 */
.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    box-shadow: var(--shadow-lg), 0 0 30px rgba(99, 102, 241, 0.4);
    animation: authIconPulse 2s ease-in-out infinite;
}

@keyframes authIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.auth-icon span {
    font-size: 32px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.auth-header h1 {
    color: var(--white);
    font-size: var(--font-size-3xl);
    font-weight: 800;
    margin-bottom: 12px;
    font-family: var(--font-display);
    background: linear-gradient(135deg, #ffffff 0%, #a5b4fc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-lg);
    line-height: 1.6;
}

/* 表单样式 */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 表单分组 */
.form-section {
    margin-bottom: 32px;
}

.section-title {
    color: var(--white);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid rgba(99, 102, 241, 0.3);
    display: flex;
    align-items: center;
    gap: 12px;
}

.required {
    color: #ff6b6b;
    font-weight: 700;
}

.optional {
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
    font-weight: 400;
}

/* 表单行 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* 表单组 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.label-text {
    color: rgba(255, 255, 255, 0.9);
}

/* 输入框包装器 */
.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 16px;
    font-size: 18px;
    z-index: 2;
    opacity: 0.7;
}

.form-input {
    width: 100%;
    padding: 16px 16px 16px 50px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: var(--white);
    font-size: var(--font-size-base);
    transition: all var(--duration-normal) var(--ease-out-expo);
    outline: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-input:focus {
    border-color: rgba(99, 102, 241, 0.6);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1), var(--shadow-lg);
    transform: translateY(-2px);
}

.form-input:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 下拉选择框 */
.form-select {
    width: 100%;
    padding: 16px 16px 16px 20px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: var(--white);
    font-size: var(--font-size-base);
    transition: all var(--duration-normal) var(--ease-out-expo);
    outline: none;
    cursor: pointer;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.form-select:focus {
    border-color: rgba(99, 102, 241, 0.6);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.form-select option {
    background: var(--gray-800);
    color: var(--white);
    padding: 8px;
}

/* 联系方式输入组 */
.contact-input-group {
    display: grid;
    grid-template-columns: 140px 1fr;
    gap: 12px;
    align-items: start;
}

.contact-type-wrapper {
    position: relative;
}

.contact-type-wrapper::after {
    content: '▼';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
    pointer-events: none;
    font-size: 12px;
}

.contact-type-wrapper .form-select {
    padding-left: 16px;
    padding-right: 40px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.contact-value-wrapper {
    transition: all var(--duration-normal);
}

.contact-value-wrapper.disabled {
    opacity: 0.5;
}

/* 密码切换按钮 */
.password-toggle {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all var(--duration-fast);
    z-index: 2;
}

.password-toggle:hover {
    color: var(--white);
    background: rgba(255, 255, 255, 0.1);
}

.toggle-icon {
    font-size: 16px;
}

/* 表单选项 */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
}

/* 复选框样式 */
.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    user-select: none;
}

.checkbox-wrapper input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    position: relative;
    transition: all var(--duration-normal);
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-color: #6366f1;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

.checkbox-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: var(--font-size-sm);
}

/* 忘记密码链接 */
.forgot-password {
    color: rgba(99, 102, 241, 0.8);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--duration-fast);
}

.forgot-password:hover {
    color: #6366f1;
}

/* 协议同意 */
.form-agreement {
    margin: 24px 0;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 提交按钮 */
.auth-submit {
    margin-top: 8px;
    position: relative;
    overflow: hidden;
}

.btn-loading {
    display: flex;
    align-items: center;
    gap: 8px;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 认证页面底部 */
.auth-footer {
    text-align: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-footer p {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
}

.auth-link {
    color: #6366f1;
    text-decoration: none;
    font-weight: 500;
    transition: color var(--duration-fast);
}

.auth-link:hover {
    color: #8b5cf6;
    text-decoration: underline;
}

/* 装饰元素 */
.auth-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.decoration-item {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    animation: decorationFloat 6s ease-in-out infinite;
}

.decoration-1 {
    width: 120px;
    height: 120px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.decoration-2 {
    width: 80px;
    height: 80px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.decoration-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes decorationFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.6;
    }
}

/* 认证页面页脚 */
.auth-page-footer {
    text-align: center;
    padding: 20px 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.auth-page-footer p {
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
}

/* 文本域样式 */
.form-textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--white);
    font-size: var(--font-size-base);
    font-family: inherit;
    resize: vertical;
    transition: all var(--duration-normal) var(--ease-out-expo);
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* 字符计数器 */
.char-counter {
    text-align: right;
    margin-top: 8px;
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.6);
}

.char-counter.warning {
    color: #f59e0b;
}

.char-counter.danger {
    color: #ef4444;
}

/* 字段错误样式 */
.field-error {
    color: #ef4444;
    font-size: var(--font-size-sm);
    margin-top: 4px;
    animation: errorSlideIn 0.3s ease-out;
}

.form-input.error,
.form-textarea.error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1) !important;
}

@keyframes errorSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-container {
        padding: 20px 16px;
    }
    
    .auth-card {
        padding: 32px 24px;
    }
    
    .register-card {
        max-width: 100%;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .contact-input-group {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .form-options {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .auth-icon {
        width: 60px;
        height: 60px;
    }
    
    .auth-icon span {
        font-size: 24px;
    }
    
    .auth-header h1 {
        font-size: var(--font-size-2xl);
    }

    .form-textarea {
        min-height: 80px;
    }
}
