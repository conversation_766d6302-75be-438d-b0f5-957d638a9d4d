<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的主页 - NEXUS 创业者社区</title>
    <link rel="stylesheet" href="../../../shared/css/main-theme.css">
    <link rel="stylesheet" href="../css/entrepreneurship.css">
    <link rel="stylesheet" href="../css/auth.css">
    <link rel="stylesheet" href="../css/profile.css">
</head>
<body>
    <!-- 导航栏将通过JavaScript动态插入 -->

    <!-- 主要内容 -->
    <div class="main-container">
        <div class="profile-container">
            <!-- 头部 -->
            <div class="profile-header">
                <h2 class="profile-title">我的主页</h2>
                <p class="profile-subtitle">管理您的个人信息</p>
            </div>

            <!-- 个人信息表单 -->
            <form id="profileForm" class="profile-form">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h3 class="section-title">基本信息</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="nickname">
                                <span class="label-text">昵称</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">👤</span>
                                <input 
                                    type="text" 
                                    id="nickname" 
                                    name="nickname" 
                                    class="form-input" 
                                    placeholder="请输入您的昵称"
                                    maxlength="100"
                                    required
                                >
                            </div>
                            <div class="char-counter">
                                <span id="nicknameCounter">0</span>/100
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="username">
                                <span class="label-text">账号</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">🆔</span>
                                <input 
                                    type="text" 
                                    id="username" 
                                    name="username" 
                                    class="form-input" 
                                    placeholder="账号不可修改"
                                    readonly
                                    disabled
                                >
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 个人资料 -->
                <div class="form-section">
                    <h3 class="section-title">个人资料</h3>
                    
                    <div class="form-group">
                        <label class="form-label" for="bio">
                            <span class="label-text">个人标签</span>
                        </label>
                        <div class="input-wrapper">
                            <span class="input-icon">📝</span>
                            <input
                                type="text"
                                id="bio"
                                name="bio"
                                class="form-input"
                                placeholder="如：AI科技创业者、电商运营专家等"
                                maxlength="255"
                            >
                        </div>
                        <div class="char-counter">
                            <span id="bioCounter">0</span>/255
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="mainBusiness">
                                <span class="label-text">主业</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">🚀</span>
                                <input 
                                    type="text" 
                                    id="mainBusiness" 
                                    name="mainBusiness" 
                                    class="form-input" 
                                    placeholder="如：AI智能助手平台"
                                    maxlength="255"
                                >
                            </div>
                            <div class="char-counter">
                                <span id="mainBusinessCounter">0</span>/255
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="sideBusiness">
                                <span class="label-text">副业</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">💼</span>
                                <input 
                                    type="text" 
                                    id="sideBusiness" 
                                    name="sideBusiness" 
                                    class="form-input" 
                                    placeholder="如：技术咨询服务"
                                    maxlength="255"
                                >
                            </div>
                            <div class="char-counter">
                                <span id="sideBusinessCounter">0</span>/255
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="location">
                            <span class="label-text">所在地</span>
                        </label>
                        <div class="input-wrapper">
                            <span class="input-icon">📍</span>
                            <input
                                type="text"
                                id="location"
                                name="location"
                                class="form-input"
                                placeholder="如：北京、上海，越详细越好"
                                maxlength="100"
                            >
                        </div>
                        <div class="char-counter">
                            <span id="locationCounter">0</span>/100
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="contactType">
                            <span class="label-text">联系方式</span>
                        </label>
                        <div class="contact-input-group">
                            <div class="input-wrapper contact-type-wrapper">
                                <select id="contactType" name="contactType" class="form-select">
                                    <option value="">选择类型</option>
                                    <option value="phone">电话</option>
                                    <option value="qq">QQ</option>
                                    <option value="wechat">微信</option>
                                </select>
                            </div>
                            <div class="input-wrapper contact-value-wrapper">
                                <span class="input-icon">📱</span>
                                <input 
                                    type="text" 
                                    id="contactValue" 
                                    name="contactValue" 
                                    class="form-input" 
                                    placeholder="请输入联系方式"
                                    maxlength="100"
                                >
                            </div>
                        </div>
                        <div class="char-counter">
                            <span id="contactValueCounter">0</span>/100
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="description">
                            <span class="label-text">详细描述</span>
                        </label>
                        <div class="input-wrapper">
                            <textarea
                                id="description"
                                name="description"
                                class="form-textarea"
                                placeholder="详细描述您的职业背景、专业能力、可提供的资源或寻求的合作等（最多1000字）"
                                maxlength="1000"
                                rows="4"
                            ></textarea>
                            <div class="char-counter">
                                <span id="descriptionCounter">0</span>/1000
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <span class="btn-text">保存修改</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="loading-spinner"></span>
                            保存中...
                        </span>
                    </button>
                    <button type="button" class="btn btn-secondary btn-lg" id="changePasswordBtn">
                        修改密码
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 修改密码模态框 -->
    <div id="passwordModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>修改密码</h3>
                <button class="modal-close" id="closePasswordModal">&times;</button>
            </div>
            <form id="passwordForm" class="modal-form">
                <div class="form-group">
                    <label class="form-label">当前密码</label>
                    <div class="input-wrapper">
                        <span class="input-icon">🔒</span>
                        <input 
                            type="password" 
                            id="currentPassword" 
                            name="currentPassword" 
                            class="form-input" 
                            placeholder="请输入当前密码"
                            required
                        >
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">新密码</label>
                    <div class="input-wrapper">
                        <span class="input-icon">🔑</span>
                        <input 
                            type="password" 
                            id="newPassword" 
                            name="newPassword" 
                            class="form-input" 
                            placeholder="请输入新密码（6-50位）"
                            minlength="6"
                            maxlength="50"
                            required
                        >
                    </div>
                    <div class="char-counter">
                        <span id="newPasswordCounter">0</span>/50
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">确认新密码</label>
                    <div class="input-wrapper">
                        <span class="input-icon">🔐</span>
                        <input 
                            type="password" 
                            id="confirmNewPassword" 
                            name="confirmNewPassword" 
                            class="form-input" 
                            placeholder="请再次输入新密码"
                            maxlength="50"
                            required
                        >
                    </div>
                    <div class="char-counter">
                        <span id="confirmNewPasswordCounter">0</span>/50
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn btn-primary">
                        <span class="btn-text">确认修改</span>
                        <span class="btn-loading" style="display: none;">
                            <span class="loading-spinner"></span>
                            修改中...
                        </span>
                    </button>
                    <button type="button" class="btn btn-secondary" id="cancelPasswordChange">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../../../shared/js/main-theme.js"></script>
    <script src="../../../shared/components/navbar.js"></script>
    <script src="../js/profile.js"></script>
</body>
</html>
