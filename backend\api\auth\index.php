<?php
/**
 * 认证API路由
 * 创业者社区平台
 */

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../../controllers/auth/AuthController.php';

try {
    $authController = new AuthController();
    
    // 获取请求路径
    $requestUri = $_SERVER['REQUEST_URI'];
    $path = parse_url($requestUri, PHP_URL_PATH);
    
    // 移除基础路径
    $basePath = '/backend/api/auth';
    $route = str_replace($basePath, '', $path);
    
    // 路由分发
    switch ($route) {
        case '/register':
            $authController->register();
            break;
            
        case '/login':
            $authController->login();
            break;
            
        case '/logout':
            $authController->logout();
            break;
            
        case '/verify':
            $authController->verifyToken();
            break;
            
        default:
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => '接口不存在'
            ]);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '服务器内部错误: ' . $e->getMessage()
    ]);
}
