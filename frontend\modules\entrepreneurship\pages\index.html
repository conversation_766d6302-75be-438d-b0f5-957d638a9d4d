<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创业社区 - NEXUS</title>
    <link rel="stylesheet" href="../../../shared/css/main-theme.css">
    <link rel="stylesheet" href="../css/entrepreneurship.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container flex justify-between items-center">
            <div class="text-2xl font-bold" style="color: var(--white); font-family: var(--font-display);">
                <a href="../../../../index.html" style="color: inherit; text-decoration: none;">NEXUS</a>
            </div>
            <div class="flex gap-8">
                <a href="#" class="nav-item active">创业社区</a>
                <a href="#" class="nav-item">我的主页</a>
                <a href="#" class="nav-item">发现</a>
                <a href="#" class="nav-item">消息</a>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container py-8">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
            <h1 style="font-family: var(--font-display); font-size: var(--font-size-4xl); font-weight: 800; margin-bottom: 16px; background: linear-gradient(135deg, #ffffff 0%, #a5b4fc 50%, #ec4899 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                创业者社区
            </h1>
            <p style="color: rgba(255, 255, 255, 0.8); font-size: var(--font-size-lg); font-weight: 300;">
                发现志同道合的创业伙伴，分享经验，共同成长
            </p>
        </div>

        <!-- 功能导航区域 -->
        <div class="function-nav mb-12">
            <div class="nav-tabs">
                <button class="tab-btn active" data-tab="entrepreneurs">创业者</button>
                <button class="tab-btn" data-tab="posts">社区动态</button>
                <button class="tab-btn" data-tab="resources">资源合作</button>
                <button class="tab-btn" data-tab="danmaku">弹幕激励</button>
            </div>
        </div>

        <!-- 创业者展示区域 -->
        <div id="entrepreneurs" class="tab-content active">
            <div class="section-header mb-8">
                <h2 style="color: var(--white); font-size: var(--font-size-2xl); font-weight: 700; margin-bottom: 8px;">
                    优秀创业者
                </h2>
                <p style="color: rgba(255, 255, 255, 0.7);">发现志同道合的创业伙伴</p>
            </div>
            
            <div class="entrepreneurs-grid">
                <!-- 创业者卡片示例 -->
                <div class="entrepreneur-card">
                    <div class="card-header">
                        <div class="avatar">
                            <img src="https://via.placeholder.com/80x80" alt="创业者头像">
                            <div class="status-indicator online"></div>
                        </div>
                        <div class="user-info">
                            <h3>张创业</h3>
                            <p class="title">AI科技创业者</p>
                            <div class="location">📍 北京</div>
                        </div>
                        <button class="follow-btn">关注</button>
                    </div>
                    <div class="card-body">
                        <div class="business-info">
                            <div class="business-item">
                                <span class="label">主业</span>
                                <span class="value">AI智能助手平台</span>
                            </div>
                            <div class="business-item">
                                <span class="label">副业</span>
                                <span class="value">技术咨询服务</span>
                            </div>
                        </div>
                        <div class="tags">
                            <span class="tag">人工智能</span>
                            <span class="tag">SaaS</span>
                            <span class="tag">B2B</span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <span>👥 128 关注者</span>
                            <span>📝 23 动态</span>
                        </div>
                    </div>
                </div>

                <!-- 更多创业者卡片... -->
                <div class="entrepreneur-card">
                    <div class="card-header">
                        <div class="avatar">
                            <img src="https://via.placeholder.com/80x80" alt="创业者头像">
                            <div class="status-indicator away"></div>
                        </div>
                        <div class="user-info">
                            <h3>李电商</h3>
                            <p class="title">电商运营专家</p>
                            <div class="location">📍 上海</div>
                        </div>
                        <button class="follow-btn followed">已关注</button>
                    </div>
                    <div class="card-body">
                        <div class="business-info">
                            <div class="business-item">
                                <span class="label">主业</span>
                                <span class="value">跨境电商平台</span>
                            </div>
                            <div class="business-item">
                                <span class="label">副业</span>
                                <span class="value">电商培训课程</span>
                            </div>
                        </div>
                        <div class="tags">
                            <span class="tag">电商</span>
                            <span class="tag">跨境</span>
                            <span class="tag">培训</span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="stats">
                            <span>👥 256 关注者</span>
                            <span>📝 45 动态</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 社区动态区域 -->
        <div id="posts" class="tab-content">
            <div class="section-header mb-8">
                <h2 style="color: var(--white); font-size: var(--font-size-2xl); font-weight: 700; margin-bottom: 8px;">
                    社区动态
                </h2>
                <p style="color: rgba(255, 255, 255, 0.7);">分享创业经验，避免踩坑</p>
            </div>
            
            <div class="posts-container">
                <div class="post-card">
                    <div class="post-header">
                        <div class="author-info">
                            <img src="https://via.placeholder.com/40x40" alt="作者头像" class="author-avatar">
                            <div>
                                <h4>张创业</h4>
                                <span class="post-time">2小时前</span>
                            </div>
                        </div>
                        <span class="post-category">踩坑分享</span>
                    </div>
                    <div class="post-content">
                        <h3>AI创业的三个大坑，新手必看！</h3>
                        <p>刚开始做AI项目时，我踩了不少坑。今天分享给大家，希望能帮助到正在创业路上的朋友们...</p>
                    </div>
                    <div class="post-footer">
                        <div class="post-stats">
                            <span>👍 128</span>
                            <span>💬 23</span>
                            <span>🔄 45</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 资源合作区域 -->
        <div id="resources" class="tab-content">
            <div class="section-header mb-8">
                <h2 style="color: var(--white); font-size: var(--font-size-2xl); font-weight: 700; margin-bottom: 8px;">
                    资源合作
                </h2>
                <p style="color: rgba(255, 255, 255, 0.7);">寻找合作伙伴，共享资源</p>
            </div>
            
            <div class="resources-grid">
                <div class="resource-card">
                    <div class="resource-header">
                        <span class="resource-type">寻求合作</span>
                        <span class="urgency high">紧急</span>
                    </div>
                    <h3>寻找技术合伙人</h3>
                    <p>AI教育项目寻找技术合伙人，有相关经验优先...</p>
                    <div class="resource-footer">
                        <span class="author">by 李教育</span>
                        <span class="time">1天前</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 弹幕激励区域 -->
        <div id="danmaku" class="tab-content">
            <div class="section-header mb-8">
                <h2 style="color: var(--white); font-size: var(--font-size-2xl); font-weight: 700; margin-bottom: 8px;">
                    弹幕激励
                </h2>
                <p style="color: rgba(255, 255, 255, 0.7);">发送激励弹幕，互相鼓励</p>
            </div>
            
            <div class="danmaku-container">
                <div class="danmaku-display">
                    <div class="danmaku-item">💪 加油！创业路上不孤单！</div>
                    <div class="danmaku-item">🚀 相信自己，一定能成功！</div>
                    <div class="danmaku-item">✨ 每一次努力都是成长！</div>
                </div>
                <div class="danmaku-input">
                    <input type="text" placeholder="发送激励弹幕..." class="danmaku-text">
                    <button class="send-danmaku-btn">发送</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部信息 -->
    <footer style="text-align: center; padding: 60px 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%); border-top: 1px solid rgba(255, 255, 255, 0.1); margin-top: 80px;">
        <div class="container">
            <p style="color: rgba(255, 255, 255, 0.8); font-size: var(--font-size-base);">
                &copy; 2024 NEXUS 创业者社区 · 连接创业者，共创未来
            </p>
        </div>
    </footer>

    <script src="../../../shared/js/main-theme.js"></script>
    <script src="../js/entrepreneurship.js"></script>
</body>
</html>
