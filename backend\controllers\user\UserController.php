<?php
/**
 * 用户控制器
 * 创业者社区平台
 */

require_once __DIR__ . '/../../models/user/User.php';

class UserController
{
    private $userModel;

    public function __construct()
    {
        $this->userModel = new User();
    }

    /**
     * 获取所有用户列表
     */
    public function getAllUsers()
    {
        try {
            // 获取分页参数
            $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
            $limit = isset($_GET['limit']) ? max(1, min(50, intval($_GET['limit']))) : 20;
            $offset = ($page - 1) * $limit;

            // 获取用户列表
            $users = $this->userModel->getAllActiveUsers($limit, $offset);

            // 处理用户数据
            $processedUsers = [];
            foreach ($users as $user) {
                $processedUsers[] = [
                    'id' => $user['id'],
                    'nickname' => $user['nickname'],
                    'bio' => $user['bio'],
                    'main_business' => $user['main_business'],
                    'side_business' => $user['side_business'],
                    'contact_type' => $user['contact_type'],
                    'contact_value' => $user['contact_value'],
                    'location' => $user['location'],
                    'status' => $user['status'],
                    'followers_count' => $user['followers_count'],
                    'posts_count' => $user['posts_count'],
                    'tags' => $user['tags'],
                    'created_at' => $user['created_at']
                ];
            }

            echo json_encode([
                'success' => true,
                'message' => '获取用户列表成功',
                'data' => [
                    'users' => $processedUsers,
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => count($processedUsers)
                    ]
                ]
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => '获取用户列表失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取单个用户信息
     */
    public function getUserById()
    {
        try {
            $userId = isset($_GET['id']) ? intval($_GET['id']) : 0;
            
            if (!$userId) {
                throw new Exception('用户ID不能为空');
            }

            $user = $this->userModel->getUserWithTags($userId);
            
            if (!$user) {
                throw new Exception('用户不存在');
            }

            // 处理用户数据（隐藏敏感信息）
            $userData = [
                'id' => $user['id'],
                'nickname' => $user['nickname'],
                'bio' => $user['bio'],
                'main_business' => $user['main_business'],
                'side_business' => $user['side_business'],
                'location' => $user['location'],
                'status' => $user['status'],
                'followers_count' => $user['followers_count'],
                'following_count' => $user['following_count'],
                'posts_count' => $user['posts_count'],
                'tags' => $user['tags'],
                'created_at' => $user['created_at']
            ];

            echo json_encode([
                'success' => true,
                'message' => '获取用户信息成功',
                'data' => $userData
            ]);

        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 关注用户
     */
    public function followUser()
    {
        try {
            // 获取当前用户ID（从token验证）
            $currentUserId = $this->getCurrentUserId();
            if (!$currentUserId) {
                throw new Exception('请先登录');
            }

            // 获取要关注的用户ID
            $input = json_decode(file_get_contents('php://input'), true);
            $targetUserId = isset($input['user_id']) ? intval($input['user_id']) : 0;

            if (!$targetUserId) {
                throw new Exception('用户ID不能为空');
            }

            if ($currentUserId == $targetUserId) {
                throw new Exception('不能关注自己');
            }

            // 检查目标用户是否存在
            $targetUser = $this->userModel->findById($targetUserId);
            if (!$targetUser) {
                throw new Exception('目标用户不存在');
            }

            // 执行关注操作
            $result = $this->userModel->followUser($currentUserId, $targetUserId);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => '关注成功'
                ]);
            } else {
                throw new Exception('关注失败，可能已经关注过了');
            }

        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 取消关注用户
     */
    public function unfollowUser()
    {
        try {
            // 获取当前用户ID
            $currentUserId = $this->getCurrentUserId();
            if (!$currentUserId) {
                throw new Exception('请先登录');
            }

            // 获取要取消关注的用户ID
            $input = json_decode(file_get_contents('php://input'), true);
            $targetUserId = isset($input['user_id']) ? intval($input['user_id']) : 0;

            if (!$targetUserId) {
                throw new Exception('用户ID不能为空');
            }

            // 执行取消关注操作
            $result = $this->userModel->unfollowUser($currentUserId, $targetUserId);

            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => '取消关注成功'
                ]);
            } else {
                throw new Exception('取消关注失败');
            }

        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取当前用户ID
     */
    private function getCurrentUserId()
    {
        // 从Authorization头获取token
        $headers = getallheaders();
        if (!isset($headers['Authorization'])) {
            return null;
        }

        $token = str_replace('Bearer ', '', $headers['Authorization']);
        
        try {
            $payload = json_decode(base64_decode($token), true);
            if (!$payload || !isset($payload['user_id'])) {
                return null;
            }

            // 检查token是否过期
            if (time() - $payload['timestamp'] > 24 * 60 * 60) {
                return null;
            }

            return $payload['user_id'];
        } catch (Exception $e) {
            return null;
        }
    }
}
