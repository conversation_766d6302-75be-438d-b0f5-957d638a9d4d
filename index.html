<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NEXUS - 创业者社区平台</title>
    <link rel="stylesheet" href="frontend/shared/css/main-theme.css">
</head>
<body>
    <!-- 导航栏将通过JavaScript动态插入 -->

    <!-- 主内容区域 -->
    <div class="container py-8">
        <!-- 欢迎区域 -->
        <div class="text-center mb-20 floating-subtle">
            <h1 style="font-family: var(--font-display); font-size: var(--font-size-6xl); font-weight: 800; margin-bottom: 32px; background: linear-gradient(135deg, #ffffff 0%, #a5b4fc 50%, #ec4899 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; line-height: 1.1;">
                NEXUS
            </h1>
            <p class="text-2xl" style="color: rgba(255, 255, 255, 0.8); line-height: 1.6; margin-bottom: 32px; font-weight: 300;">
                创业者社区平台 · 发现志同道合的创业伙伴
            </p>
            <div style="margin-top: 40px;">
                <a href="frontend/modules/entrepreneurship/pages/index.html" class="btn btn-primary btn-lg" style="font-size: 1.125rem; padding: 20px 40px;">
                    进入创业社区
                </a>
            </div>
        </div>

        <!-- 功能预览 -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 32px; margin-bottom: 64px;">
            <div class="card subtle-hover scale-on-hover">
                <div class="card-header">
                    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 16px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #10b981, #0ea5e9); border-radius: 12px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);">
                            <span style="color: white; font-size: 20px;">🚀</span>
                        </div>
                        <h3 style="font-size: var(--font-size-xl); font-weight: 700; color: var(--white);">创业社区</h3>
                    </div>
                    <div style="width: 60px; height: 4px; background: linear-gradient(135deg, #10b981, #0ea5e9); border-radius: 2px;"></div>
                </div>
                <div class="card-body">
                    <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.7; margin-bottom: 32px; font-size: var(--font-size-lg);">
                        发现志同道合的创业者，展示主业副业，互相关注获取联系方式，分享行业经验与资源。
                    </p>
                    <a href="frontend/modules/entrepreneurship/pages/index.html" class="btn btn-primary">进入社区</a>
                </div>
            </div>

            <div class="card subtle-hover scale-on-hover">
                <div class="card-header">
                    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 16px;">
                        <div style="width: 50px; height: 50px; background: linear-gradient(135deg, #ec4899, #f59e0b); border-radius: 12px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 20px rgba(236, 72, 153, 0.3);">
                            <span style="color: white; font-size: 20px;">💕</span>
                        </div>
                        <h3 style="font-size: var(--font-size-xl); font-weight: 700; color: var(--white);">相亲交友</h3>
                    </div>
                    <div style="width: 60px; height: 4px; background: linear-gradient(135deg, #ec4899, #f59e0b); border-radius: 2px;"></div>
                </div>
                <div class="card-body">
                    <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.7; margin-bottom: 32px; font-size: var(--font-size-lg);">
                        未来扩展的相亲交友功能模块，采用相同的现代化设计语言和先进的交互模式。
                    </p>
                    <button class="btn btn-outline">COMING SOON</button>
                </div>
            </div>
        </div>


    </div>

    <!-- 底部信息 -->
    <footer style="text-align: center; padding: 80px 0; background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%); border-top: 1px solid rgba(255, 255, 255, 0.1); margin-top: 80px;">
        <div class="container">
            <div style="margin-bottom: 32px;">
                <div style="width: 120px; height: 6px; background: linear-gradient(135deg, #6366f1, #ec4899); border-radius: 3px; margin: 0 auto;"></div>
            </div>
            <h3 style="font-family: var(--font-display); font-size: var(--font-size-2xl); font-weight: 700; color: var(--white); margin-bottom: 16px;">
                NEXUS
            </h3>
            <p style="color: rgba(255, 255, 255, 0.8); font-size: var(--font-size-lg); margin-bottom: 8px;">
                &copy; 2024 NEXUS · 创业者社区平台
            </p>
            <p style="color: rgba(255, 255, 255, 0.6); font-size: var(--font-size-base); text-transform: uppercase; letter-spacing: 0.1em;">
                CONNECT · CREATE · COLLABORATE
            </p>
        </div>
    </footer>

    <script src="frontend/shared/js/main-theme.js"></script>
    <script src="frontend/shared/components/navbar.js"></script>
    <script>
        // 初始化首页导航栏
        document.addEventListener('DOMContentLoaded', function() {
            new NavbarComponent({
                pageType: 'home',
                currentPage: 'entrepreneurship'
            });
        });
    </script>
</body>
</html>
