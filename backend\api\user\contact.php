<?php
/**
 * 获取用户联系方式API
 * 创业者社区平台
 */

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许GET请求'
    ]);
    exit();
}

require_once __DIR__ . '/../../models/user/User.php';

try {
    // 获取当前用户ID
    $currentUserId = getCurrentUserId();
    if (!$currentUserId) {
        throw new Exception('请先登录');
    }

    // 获取目标用户ID
    $targetUserId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    if (!$targetUserId) {
        throw new Exception('用户ID不能为空');
    }

    $userModel = new User();
    
    // 检查是否关注了目标用户
    if (!$userModel->isFollowing($currentUserId, $targetUserId)) {
        throw new Exception('需要关注后才能查看联系方式');
    }

    // 获取目标用户信息
    $targetUser = $userModel->findById($targetUserId);
    if (!$targetUser) {
        throw new Exception('用户不存在');
    }

    // 返回联系方式
    echo json_encode([
        'success' => true,
        'message' => '获取联系方式成功',
        'data' => [
            'contact_type' => $targetUser['contact_type'],
            'contact_value' => $targetUser['contact_value']
        ]
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 获取当前用户ID
 */
function getCurrentUserId()
{
    // 从Authorization头获取token
    $headers = getallheaders();
    if (!isset($headers['Authorization'])) {
        return null;
    }

    $token = str_replace('Bearer ', '', $headers['Authorization']);
    
    try {
        $payload = json_decode(base64_decode($token), true);
        if (!$payload || !isset($payload['user_id'])) {
            return null;
        }

        // 检查token是否过期
        if (time() - $payload['timestamp'] > 24 * 60 * 60) {
            return null;
        }

        return $payload['user_id'];
    } catch (Exception $e) {
        return null;
    }
}
