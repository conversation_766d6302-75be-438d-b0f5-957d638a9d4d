<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - NEXUS 创业者社区</title>
    <link rel="stylesheet" href="../../../shared/css/main-theme.css">
    <link rel="stylesheet" href="../css/auth.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container flex justify-between items-center">
            <div class="text-2xl font-bold" style="color: var(--white); font-family: var(--font-display);">
                <a href="../../../../index.html" style="color: inherit; text-decoration: none;">NEXUS</a>
            </div>
            <div class="flex gap-8">
                <a href="index.html" class="nav-item">返回社区</a>
                <a href="login.html" class="nav-item">已有账号</a>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="auth-container">
        <div class="auth-card register-card">
            <!-- 标题区域 -->
            <div class="auth-header">
                <div class="auth-icon">
                    <span>✨</span>
                </div>
                <h1>加入我们</h1>
                <p>创建您的创业者账号，开启创业社交之旅</p>
            </div>

            <!-- 注册表单 -->
            <form class="auth-form" id="registerForm">
                <!-- 必填信息 -->
                <div class="form-section">
                    <h3 class="section-title">基本信息 <span class="required">*必填</span></h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="nickname">
                                <span class="label-text">昵称</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">👤</span>
                                <input 
                                    type="text" 
                                    id="nickname" 
                                    name="nickname" 
                                    class="form-input" 
                                    placeholder="请输入您的昵称"
                                    required
                                >
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="username">
                                <span class="label-text">账号</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">🆔</span>
                                <input 
                                    type="text" 
                                    id="username" 
                                    name="username" 
                                    class="form-input" 
                                    placeholder="请输入账号（用于登录）"
                                    required
                                >
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="password">
                                <span class="label-text">密码</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">🔒</span>
                                <input 
                                    type="password" 
                                    id="password" 
                                    name="password" 
                                    class="form-input" 
                                    placeholder="请设置密码（至少6位）"
                                    required
                                    minlength="6"
                                >
                                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                    <span class="toggle-icon">👁️</span>
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="confirmPassword">
                                <span class="label-text">确认密码</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">🔐</span>
                                <input 
                                    type="password" 
                                    id="confirmPassword" 
                                    name="confirmPassword" 
                                    class="form-input" 
                                    placeholder="请再次输入密码"
                                    required
                                >
                                <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                    <span class="toggle-icon">👁️</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 选填信息 -->
                <div class="form-section">
                    <h3 class="section-title">个人资料 <span class="optional">选填</span></h3>
                    
                    <div class="form-group">
                        <label class="form-label" for="bio">
                            <span class="label-text">个人备注</span>
                        </label>
                        <div class="input-wrapper">
                            <span class="input-icon">📝</span>
                            <input 
                                type="text" 
                                id="bio" 
                                name="bio" 
                                class="form-input" 
                                placeholder="如：AI科技创业者、电商运营专家等"
                            >
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="mainBusiness">
                                <span class="label-text">主业</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">🚀</span>
                                <input 
                                    type="text" 
                                    id="mainBusiness" 
                                    name="mainBusiness" 
                                    class="form-input" 
                                    placeholder="如：AI智能助手平台"
                                >
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="sideBusiness">
                                <span class="label-text">副业</span>
                            </label>
                            <div class="input-wrapper">
                                <span class="input-icon">💼</span>
                                <input 
                                    type="text" 
                                    id="sideBusiness" 
                                    name="sideBusiness" 
                                    class="form-input" 
                                    placeholder="如：技术咨询服务"
                                >
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="location">
                            <span class="label-text">所在地</span>
                        </label>
                        <div class="input-wrapper">
                            <span class="input-icon">📍</span>
                            <input
                                type="text"
                                id="location"
                                name="location"
                                class="form-input"
                                placeholder="如：北京、上海、深圳"
                            >
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="contactType">
                            <span class="label-text">联系方式</span>
                        </label>
                        <div class="contact-input-group">
                            <div class="input-wrapper contact-type-wrapper">
                                <select id="contactType" name="contactType" class="form-select">
                                    <option value="">选择类型</option>
                                    <option value="phone">电话</option>
                                    <option value="qq">QQ</option>
                                    <option value="wechat">微信</option>
                                </select>
                            </div>
                            <div class="input-wrapper contact-value-wrapper">
                                <span class="input-icon">📱</span>
                                <input 
                                    type="text" 
                                    id="contactValue" 
                                    name="contactValue" 
                                    class="form-input" 
                                    placeholder="请输入联系方式"
                                    disabled
                                >
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 协议同意 -->
                <div class="form-agreement">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" id="agreement" name="agreement" required>
                        <span class="checkmark"></span>
                        <span class="checkbox-text">
                            我已阅读并同意 <a href="#" class="auth-link">用户协议</a> 和 <a href="#" class="auth-link">隐私政策</a>
                        </span>
                    </label>
                </div>

                <button type="submit" class="btn btn-primary btn-lg auth-submit">
                    <span class="btn-text">创建账号</span>
                    <span class="btn-loading" style="display: none;">
                        <span class="loading-spinner"></span>
                        注册中...
                    </span>
                </button>
            </form>

            <!-- 底部链接 -->
            <div class="auth-footer">
                <p>已有账号？ <a href="login.html" class="auth-link">立即登录</a></p>
            </div>
        </div>

        <!-- 装饰元素 -->
        <div class="auth-decoration">
            <div class="decoration-item decoration-1"></div>
            <div class="decoration-item decoration-2"></div>
            <div class="decoration-item decoration-3"></div>
        </div>
    </div>

    <!-- 底部信息 -->
    <footer class="auth-page-footer">
        <div class="container">
            <p>&copy; 2024 NEXUS 创业者社区 · 连接创业者，共创未来</p>
        </div>
    </footer>

    <script src="../../../shared/js/main-theme.js"></script>
    <script src="../js/auth.js"></script>
</body>
</html>
