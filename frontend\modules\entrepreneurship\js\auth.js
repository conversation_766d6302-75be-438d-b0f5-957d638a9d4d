/**
 * 认证页面JavaScript功能
 */

class AuthModule {
    constructor() {
        this.navbar = null;
        this.init();
    }

    async init() {
        // 初始化导航栏
        this.navbar = new NavbarComponent({ currentPage: '' });

        this.bindEvents();
        this.initContactTypeSelector();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 登录表单提交
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin(e.target);
            });
        }

        // 注册表单提交
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleRegister(e.target);
            });
        }

        // 联系方式类型选择
        const contactType = document.getElementById('contactType');
        if (contactType) {
            contactType.addEventListener('change', (e) => {
                this.handleContactTypeChange(e.target.value);
            });
        }

        // 密码确认验证
        const confirmPassword = document.getElementById('confirmPassword');
        if (confirmPassword) {
            confirmPassword.addEventListener('input', (e) => {
                this.validatePasswordMatch();
            });
        }

        // 实时表单验证
        const inputs = document.querySelectorAll('.form-input');
        inputs.forEach(input => {
            input.addEventListener('blur', (e) => {
                this.validateField(e.target);
            });
        });

        // 字符计数配置
        const fieldConfigs = [
            { id: 'nickname', maxLength: 100, warningAt: 80 },
            { id: 'username', maxLength: 50, warningAt: 40 },
            { id: 'password', maxLength: 50, warningAt: 40 },
            { id: 'confirmPassword', maxLength: 50, warningAt: 40 },
            { id: 'bio', maxLength: 255, warningAt: 200 },
            { id: 'mainBusiness', maxLength: 255, warningAt: 200 },
            { id: 'sideBusiness', maxLength: 255, warningAt: 200 },
            { id: 'location', maxLength: 100, warningAt: 80 },
            { id: 'contactValue', maxLength: 100, warningAt: 80 },
            { id: 'description', maxLength: 1000, warningAt: 600, dangerAt: 800 }
        ];

        // 为每个字段添加字符计数
        fieldConfigs.forEach(config => {
            const field = document.getElementById(config.id);
            const counter = document.getElementById(config.id + 'Counter');

            if (field && counter) {
                field.addEventListener('input', (e) => {
                    const length = e.target.value.length;
                    counter.textContent = length;

                    const counterElement = counter.parentElement;
                    counterElement.classList.remove('warning', 'danger');

                    if (config.dangerAt && length > config.dangerAt) {
                        counterElement.classList.add('danger');
                    } else if (length > config.warningAt) {
                        counterElement.classList.add('warning');
                    }

                    // 实时验证字段长度
                    this.validateFieldLength(field, config);
                });
            }
        });
    }

    /**
     * 初始化联系方式选择器
     */
    initContactTypeSelector() {
        const contactValue = document.getElementById('contactValue');
        const contactValueWrapper = contactValue?.parentElement;

        if (contactValue) {
            contactValue.disabled = true;
            if (contactValueWrapper) {
                contactValueWrapper.classList.add('disabled');
            }
        }
    }

    /**
     * 处理联系方式类型变化
     */
    handleContactTypeChange(type) {
        const contactValue = document.getElementById('contactValue');
        const contactValueWrapper = contactValue.parentElement;
        const inputIcon = contactValueWrapper.querySelector('.input-icon');

        if (type) {
            contactValue.disabled = false;
            contactValueWrapper.classList.remove('disabled');
            contactValue.focus();

            // 更新图标和占位符
            switch (type) {
                case 'phone':
                    inputIcon.textContent = '📱';
                    contactValue.placeholder = '请输入手机号码';
                    contactValue.type = 'tel';
                    break;
                case 'qq':
                    inputIcon.textContent = '💬';
                    contactValue.placeholder = '请输入QQ号码';
                    contactValue.type = 'text';
                    break;
                case 'wechat':
                    inputIcon.textContent = '💚';
                    contactValue.placeholder = '请输入微信号';
                    contactValue.type = 'text';
                    break;
            }
        } else {
            contactValue.disabled = true;
            contactValueWrapper.classList.add('disabled');
            contactValue.value = '';
            inputIcon.textContent = '📱';
            contactValue.placeholder = '请输入联系方式';
        }
    }

    /**
     * 处理登录
     */
    async handleLogin(form) {
        const formData = new FormData(form);
        const loginData = {
            username: formData.get('username'),
            password: formData.get('password'),
            rememberMe: formData.get('rememberMe') === 'on'
        };

        // 显示加载状态
        this.showLoading(form);

        try {
            const response = await fetch('../../../../backend/api/auth/login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(loginData)
            });

            const result = await response.json();

            if (result.success) {
                // 保存用户信息和token
                localStorage.setItem('userToken', result.data.token);
                localStorage.setItem('userId', result.data.user_id);
                localStorage.setItem('username', result.data.username);
                localStorage.setItem('nickname', result.data.nickname);

                if (loginData.rememberMe) {
                    localStorage.setItem('rememberMe', 'true');
                }

                this.hideLoading(form);
                this.showNotification('登录成功！正在跳转...', 'success');

                // 跳转回创业社区
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            this.hideLoading(form);
            this.showNotification(error.message || '登录失败，请重试', 'error');
        }
    }

    /**
     * 处理注册
     */
    async handleRegister(form) {
        const formData = new FormData(form);

        // 验证必填字段
        if (!this.validateRequiredFields(form)) {
            return;
        }

        // 验证密码匹配
        if (!this.validatePasswordMatch()) {
            return;
        }

        // 收集表单数据
        const registerData = {
            nickname: formData.get('nickname'),
            username: formData.get('username'),
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword'),
            bio: formData.get('bio'),
            mainBusiness: formData.get('mainBusiness'),
            sideBusiness: formData.get('sideBusiness'),
            location: formData.get('location'),
            description: formData.get('description'),
            contactType: formData.get('contactType'),
            contactValue: formData.get('contactValue'),
            agreement: formData.get('agreement')
        };

        // 验证字段长度
        if (!this.validateAllFieldLengths(registerData)) {
            return;
        }

        // 显示加载状态
        this.showLoading(form);

        try {
            const response = await fetch('../../../../backend/api/auth/register.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(registerData)
            });

            const result = await response.json();

            if (result.success) {
                this.hideLoading(form);
                this.showNotification('注册成功！正在跳转到登录页面...', 'success');

                // 跳转到登录页面
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1500);
            } else {
                // 如果是表不存在的错误，尝试初始化数据库
                if (result.message && result.message.includes('Table') && result.message.includes("doesn't exist")) {
                    await this.initDatabaseAndRetry(registerData, form);
                } else {
                    throw new Error(result.message);
                }
            }

        } catch (error) {
            this.hideLoading(form);
            this.showNotification(error.message || '注册失败，请重试', 'error');
        }
    }

    /**
     * 验证必填字段
     */
    validateRequiredFields(form) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, '此字段为必填项');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        return isValid;
    }

    /**
     * 验证密码匹配
     */
    validatePasswordMatch() {
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirmPassword');
        
        if (!password || !confirmPassword) return true;

        if (password.value !== confirmPassword.value) {
            this.showFieldError(confirmPassword, '两次输入的密码不一致');
            return false;
        } else {
            this.clearFieldError(confirmPassword);
            return true;
        }
    }

    /**
     * 验证单个字段
     */
    validateField(field) {
        const value = field.value.trim();
        
        // 清除之前的错误
        this.clearFieldError(field);

        // 必填字段验证
        if (field.hasAttribute('required') && !value) {
            this.showFieldError(field, '此字段为必填项');
            return false;
        }

        // 特定字段验证
        switch (field.type) {
            case 'password':
                if (value && value.length < 6) {
                    this.showFieldError(field, '密码至少需要6位字符');
                    return false;
                }
                break;
            case 'tel':
                if (value && !/^1[3-9]\d{9}$/.test(value)) {
                    this.showFieldError(field, '请输入正确的手机号码');
                    return false;
                }
                break;
        }

        return true;
    }

    /**
     * 显示字段错误
     */
    showFieldError(field, message) {
        // 移除现有错误
        this.clearFieldError(field);

        // 添加错误样式
        field.style.borderColor = '#ff6b6b';
        field.style.boxShadow = '0 0 0 4px rgba(255, 107, 107, 0.1)';

        // 创建错误消息
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        errorElement.style.cssText = `
            color: #ff6b6b;
            font-size: 0.875rem;
            margin-top: 4px;
            animation: errorSlideIn 0.3s ease-out;
        `;

        // 插入错误消息
        field.parentElement.parentElement.appendChild(errorElement);
    }

    /**
     * 清除字段错误
     */
    clearFieldError(field) {
        // 重置字段样式
        field.style.borderColor = '';
        field.style.boxShadow = '';

        // 移除错误消息
        const errorElement = field.parentElement.parentElement.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(form) {
        const submitBtn = form.querySelector('.auth-submit');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        btnText.style.display = 'none';
        btnLoading.style.display = 'flex';
        submitBtn.disabled = true;
    }

    /**
     * 隐藏加载状态
     */
    hideLoading(form) {
        const submitBtn = form.querySelector('.auth-submit');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        btnText.style.display = 'block';
        btnLoading.style.display = 'none';
        submitBtn.disabled = false;
    }

    /**
     * 初始化数据库并重试注册
     */
    async initDatabaseAndRetry(registerData, form) {
        try {
            this.showNotification('正在初始化数据库...', 'info');

            // 初始化数据库
            const initResponse = await fetch('../../../../backend/api/init_db.php', {
                method: 'GET'
            });

            const initResult = await initResponse.json();

            if (initResult.success) {
                this.showNotification('数据库初始化成功，正在重试注册...', 'info');

                // 重试注册
                const retryResponse = await fetch('../../../../backend/api/auth/register.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(registerData)
                });

                const retryResult = await retryResponse.json();

                if (retryResult.success) {
                    this.hideLoading(form);
                    this.showNotification('注册成功！正在跳转到登录页面...', 'success');

                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 1500);
                } else {
                    throw new Error(retryResult.message);
                }
            } else {
                throw new Error('数据库初始化失败: ' + initResult.message);
            }

        } catch (error) {
            this.hideLoading(form);
            this.showNotification('初始化失败: ' + error.message, 'error');
        }
    }

    /**
     * 验证所有字段长度
     */
    validateAllFieldLengths(registerData) {
        const fieldValidations = [
            { value: registerData.nickname, maxLength: 100, name: '昵称' },
            { value: registerData.username, maxLength: 50, name: '账号' },
            { value: registerData.password, maxLength: 50, name: '密码' },
            { value: registerData.confirmPassword, maxLength: 50, name: '确认密码' },
            { value: registerData.bio, maxLength: 255, name: '个人标签' },
            { value: registerData.mainBusiness, maxLength: 255, name: '主业' },
            { value: registerData.sideBusiness, maxLength: 255, name: '副业' },
            { value: registerData.location, maxLength: 100, name: '所在地' },
            { value: registerData.contactValue, maxLength: 100, name: '联系方式' },
            { value: registerData.description, maxLength: 1000, name: '详细描述' }
        ];

        for (const validation of fieldValidations) {
            if (validation.value && validation.value.length > validation.maxLength) {
                this.showNotification(`${validation.name}不能超过${validation.maxLength}个字符`, 'error');
                return false;
            }
        }

        return true;
    }

    /**
     * 验证字段长度
     */
    validateFieldLength(field, config) {
        const length = field.value.length;
        const fieldName = this.getFieldDisplayName(config.id);

        // 移除之前的错误状态
        field.classList.remove('error');
        this.clearFieldError(field);

        if (length > config.maxLength) {
            field.classList.add('error');
            this.showFieldError(field, `${fieldName}不能超过${config.maxLength}个字符`);
            return false;
        }

        return true;
    }

    /**
     * 获取字段显示名称
     */
    getFieldDisplayName(fieldId) {
        const nameMap = {
            'nickname': '昵称',
            'username': '账号',
            'password': '密码',
            'confirmPassword': '确认密码',
            'bio': '个人标签',
            'mainBusiness': '主业',
            'sideBusiness': '副业',
            'location': '所在地',
            'contactValue': '联系方式',
            'description': '详细描述'
        };
        return nameMap[fieldId] || fieldId;
    }



    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 使用主题的通知系统
        if (typeof MainTheme !== 'undefined' && MainTheme.showNotification) {
            MainTheme.showNotification(message, type);
        } else {
            // 简单的fallback通知
            alert(message);
        }
    }
}

/**
 * 切换密码显示/隐藏
 */
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggleBtn = input.parentElement.querySelector('.password-toggle');
    const toggleIcon = toggleBtn.querySelector('.toggle-icon');

    if (input.type === 'password') {
        input.type = 'text';
        toggleIcon.textContent = '🙈';
    } else {
        input.type = 'password';
        toggleIcon.textContent = '👁️';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new AuthModule();

    // 添加错误动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes errorSlideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    `;
    document.head.appendChild(style);
});
