/**
 * 创业模块JavaScript功能
 */

class EntrepreneurshipModule {
    constructor() {
        this.currentTab = 'entrepreneurs';
        this.isLoggedIn = false; // 模拟登录状态，实际应该从后端获取
        this.init();
    }

    init() {
        this.checkLoginStatus();
        this.bindEvents();
        this.initDanmaku();
        this.loadEntrepreneurs();
    }

    /**
     * 检查登录状态
     */
    async checkLoginStatus() {
        const token = localStorage.getItem('userToken');

        if (!token) {
            this.isLoggedIn = false;
            return;
        }

        try {
            const response = await fetch('/backend/api/auth/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();

            if (result.success) {
                this.isLoggedIn = true;
                this.currentUser = result.data;
                this.updateNavForLoggedInUser();
            } else {
                // Token无效，清除本地存储
                this.clearUserData();
                this.isLoggedIn = false;
            }
        } catch (error) {
            console.error('验证登录状态失败:', error);
            this.clearUserData();
            this.isLoggedIn = false;
        }
    }

    /**
     * 清除用户数据
     */
    clearUserData() {
        localStorage.removeItem('userToken');
        localStorage.removeItem('userId');
        localStorage.removeItem('username');
        localStorage.removeItem('nickname');
        localStorage.removeItem('rememberMe');
    }

    /**
     * 更新已登录用户的导航栏
     */
    updateNavForLoggedInUser() {
        const authNavItem = document.getElementById('authNavItem');
        if (authNavItem) {
            authNavItem.textContent = '退出';
            authNavItem.href = '#';
            authNavItem.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }
    }

    /**
     * 退出登录
     */
    async logout() {
        try {
            const token = localStorage.getItem('userToken');

            if (token) {
                await fetch('/backend/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
            }
        } catch (error) {
            console.error('退出登录请求失败:', error);
        }

        // 清除本地数据
        this.clearUserData();
        this.isLoggedIn = false;
        this.showNotification('已退出登录', 'info');

        // 更新导航栏
        const authNavItem = document.getElementById('authNavItem');
        if (authNavItem) {
            authNavItem.textContent = '登录';
            authNavItem.href = 'login.html';
        }

        // 刷新页面状态
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 标签页切换
        const tabBtns = document.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // 关注按钮
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('follow-btn')) {
                this.toggleFollow(e.target);
            }
        });

        // 弹幕发送
        const sendBtn = document.querySelector('.send-danmaku-btn');
        const danmakuInput = document.querySelector('.danmaku-text');
        
        if (sendBtn) {
            sendBtn.addEventListener('click', () => {
                this.sendDanmaku();
            });
        }

        if (danmakuInput) {
            danmakuInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendDanmaku();
                }
            });
        }

        // 帖子互动
        document.addEventListener('click', (e) => {
            if (e.target.closest('.post-stats span')) {
                this.handlePostInteraction(e.target.closest('.post-stats span'));
            }
        });

        // 联系方式复制
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('contact-info') && e.target.classList.contains('visible')) {
                this.copyContactInfo(e.target);
            }
        });
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // 根据不同标签页加载对应内容
        switch (tabName) {
            case 'entrepreneurs':
                this.loadEntrepreneurs();
                break;
            case 'posts':
                this.loadPosts();
                break;
            case 'resources':
                this.loadResources();
                break;
            case 'danmaku':
                this.refreshDanmaku();
                break;
        }
    }

    /**
     * 切换关注状态
     */
    toggleFollow(btn) {
        // 检查登录状态
        if (!this.isLoggedIn) {
            this.showNotification('请先登录后再进行关注操作', 'warning');
            // 跳转到登录页面
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1500);
            return;
        }

        const isFollowed = btn.classList.contains('followed');
        const card = btn.closest('.entrepreneur-card');
        const contactInfo = card.querySelector('.contact-info');

        if (isFollowed) {
            btn.classList.remove('followed');
            btn.textContent = '关注';
            this.hideContactInfo(contactInfo);
            this.showNotification('已取消关注', 'info');
        } else {
            btn.classList.add('followed');
            btn.textContent = '已关注';
            this.showContactInfo(contactInfo);
            this.showNotification('关注成功！现在可以查看联系方式', 'success');
        }

        // 这里可以添加API调用来更新后端数据
        // this.updateFollowStatus(userId, !isFollowed);
    }

    /**
     * 显示联系方式
     */
    showContactInfo(contactElement) {
        if (contactElement) {
            contactElement.classList.remove('hidden');
            contactElement.classList.add('visible');
            // 模拟真实联系方式（实际应该从API获取）
            const realContacts = [
                '微信: entrepreneur_001',
                '邮箱: <EMAIL>',
                '电话: 138****8888',
                'QQ: 123456789'
            ];
            const randomContact = realContacts[Math.floor(Math.random() * realContacts.length)];
            contactElement.textContent = randomContact;
        }
    }

    /**
     * 隐藏联系方式
     */
    hideContactInfo(contactElement) {
        if (contactElement) {
            contactElement.classList.remove('visible');
            contactElement.classList.add('hidden');
            contactElement.textContent = '★ ★ ★ ★ ★ ★ ★';
        }
    }

    /**
     * 复制联系方式
     */
    copyContactInfo(contactElement) {
        const contactText = contactElement.textContent;

        // 尝试使用现代的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(contactText).then(() => {
                this.showNotification('联系方式已复制到剪贴板', 'success');
                this.showCopyAnimation(contactElement);
            }).catch(() => {
                this.fallbackCopyTextToClipboard(contactText, contactElement);
            });
        } else {
            // 降级方案
            this.fallbackCopyTextToClipboard(contactText, contactElement);
        }
    }

    /**
     * 降级复制方案
     */
    fallbackCopyTextToClipboard(text, element) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            this.showNotification('联系方式已复制到剪贴板', 'success');
            this.showCopyAnimation(element);
        } catch (err) {
            this.showNotification('复制失败，请手动复制', 'warning');
        }

        document.body.removeChild(textArea);
    }

    /**
     * 显示复制动画
     */
    showCopyAnimation(element) {
        const originalTransform = element.style.transform;
        element.style.transform = 'scale(1.1)';
        element.style.background = 'linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(16, 185, 129, 0.2))';

        setTimeout(() => {
            element.style.transform = originalTransform;
            element.style.background = '';
        }, 200);
    }

    /**
     * 加载创业者数据
     */
    loadEntrepreneurs() {
        // 这里应该从API获取数据，现在使用模拟数据
        console.log('加载创业者数据...');

        // 模拟加载动画
        const grid = document.querySelector('.entrepreneurs-grid');
        if (grid) {
            grid.style.opacity = '0.5';
            setTimeout(() => {
                grid.style.opacity = '1';
                // 动态添加更多创业者卡片
                this.addSampleEntrepreneurs(grid);
            }, 500);
        }
    }

    /**
     * 添加示例创业者
     */
    addSampleEntrepreneurs(grid) {
        const sampleEntrepreneurs = [
            {
                name: '王技术',
                title: 'AI算法工程师',
                location: '深圳',
                avatar: 'https://via.placeholder.com/80x80',
                status: 'online',
                mainBusiness: 'AI视觉识别平台',
                sideBusiness: '技术培训咨询',
                tags: ['人工智能', '计算机视觉', '深度学习'],
                followers: 89,
                posts: 12,
                isFollowed: false
            },
            {
                name: '陈金融',
                title: '金融科技创业者',
                location: '杭州',
                avatar: 'https://via.placeholder.com/80x80',
                status: 'away',
                mainBusiness: '数字货币交易平台',
                sideBusiness: '投资理财顾问',
                tags: ['金融科技', 'FinTech', '区块链'],
                followers: 156,
                posts: 28,
                isFollowed: false
            }
        ];

        // 检查是否已经添加过示例创业者
        if (grid.children.length <= 2) {
            sampleEntrepreneurs.forEach(entrepreneur => {
                const entrepreneurElement = this.createEntrepreneurCard(entrepreneur);
                grid.appendChild(entrepreneurElement);
            });
        }
    }

    /**
     * 创建创业者卡片元素
     */
    createEntrepreneurCard(entrepreneur) {
        const cardDiv = document.createElement('div');
        cardDiv.className = 'entrepreneur-card';

        const followBtnClass = entrepreneur.isFollowed ? 'follow-btn followed' : 'follow-btn';
        const followBtnText = entrepreneur.isFollowed ? '已关注' : '关注';
        const contactInfoClass = entrepreneur.isFollowed ? 'contact-info visible' : 'contact-info hidden';
        const contactInfoText = entrepreneur.isFollowed ? '微信: ' + entrepreneur.name.toLowerCase() + '_contact' : '★ ★ ★ ★ ★ ★ ★';

        cardDiv.innerHTML = `
            <div class="card-header">
                <div class="avatar">
                    <img src="${entrepreneur.avatar}" alt="创业者头像">
                    <div class="status-indicator ${entrepreneur.status}"></div>
                </div>
                <div class="user-info">
                    <h3>${entrepreneur.name}</h3>
                    <p class="title">${entrepreneur.title}</p>
                    <div class="location">📍 ${entrepreneur.location}</div>
                </div>
                <button class="${followBtnClass}">${followBtnText}</button>
            </div>
            <div class="card-body">
                <div class="business-info">
                    <div class="business-item">
                        <span class="label">主业</span>
                        <span class="value">${entrepreneur.mainBusiness}</span>
                    </div>
                    <div class="business-item">
                        <span class="label">副业</span>
                        <span class="value">${entrepreneur.sideBusiness}</span>
                    </div>
                    <div class="business-item">
                        <span class="label">联系方式</span>
                        <span class="value ${contactInfoClass}">${contactInfoText}</span>
                    </div>
                </div>
                <div class="tags">
                    ${entrepreneur.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <span>👥 ${entrepreneur.followers} 关注者</span>
                    <span>📝 ${entrepreneur.posts} 动态</span>
                </div>
            </div>
        `;

        return cardDiv;
    }

    /**
     * 加载帖子数据
     */
    loadPosts() {
        console.log('加载社区动态...');
        
        // 模拟数据加载
        const container = document.querySelector('.posts-container');
        if (container) {
            // 可以在这里动态添加更多帖子
            this.addSamplePosts(container);
        }
    }

    /**
     * 添加示例帖子
     */
    addSamplePosts(container) {
        const samplePosts = [
            {
                author: '李电商',
                avatar: 'https://via.placeholder.com/40x40',
                time: '5小时前',
                category: '资源分享',
                title: '电商运营必备工具清单',
                content: '整理了一份电商运营的工具清单，包括数据分析、客服、营销等各个方面...',
                stats: { likes: 89, comments: 15, shares: 23 }
            },
            {
                author: '王技术',
                avatar: 'https://via.placeholder.com/40x40',
                time: '1天前',
                category: '技术分享',
                title: '如何快速搭建MVP产品',
                content: '分享一下我们团队快速搭建MVP的经验，从需求分析到产品上线只用了2周...',
                stats: { likes: 156, comments: 32, shares: 45 }
            }
        ];

        // 检查是否已经添加过示例帖子
        if (container.children.length <= 1) {
            samplePosts.forEach(post => {
                const postElement = this.createPostElement(post);
                container.appendChild(postElement);
            });
        }
    }

    /**
     * 创建帖子元素
     */
    createPostElement(post) {
        const postDiv = document.createElement('div');
        postDiv.className = 'post-card';
        postDiv.innerHTML = `
            <div class="post-header">
                <div class="author-info">
                    <img src="${post.avatar}" alt="作者头像" class="author-avatar">
                    <div>
                        <h4>${post.author}</h4>
                        <span class="post-time">${post.time}</span>
                    </div>
                </div>
                <span class="post-category">${post.category}</span>
            </div>
            <div class="post-content">
                <h3>${post.title}</h3>
                <p>${post.content}</p>
            </div>
            <div class="post-footer">
                <div class="post-stats">
                    <span>👍 ${post.stats.likes}</span>
                    <span>💬 ${post.stats.comments}</span>
                    <span>🔄 ${post.stats.shares}</span>
                </div>
            </div>
        `;
        return postDiv;
    }

    /**
     * 加载资源合作数据
     */
    loadResources() {
        console.log('加载资源合作信息...');
        
        // 这里可以添加更多资源合作卡片
        const grid = document.querySelector('.resources-grid');
        if (grid && grid.children.length <= 1) {
            this.addSampleResources(grid);
        }
    }

    /**
     * 添加示例资源
     */
    addSampleResources(grid) {
        const sampleResources = [
            {
                type: '提供资源',
                urgency: 'medium',
                title: '免费提供办公场地',
                content: '我们公司有多余的办公位置，可以免费提供给初创团队使用...',
                author: '张场地',
                time: '2天前'
            },
            {
                type: '寻求投资',
                urgency: 'high',
                title: '教育科技项目寻求天使投资',
                content: '我们的在线教育平台已有10万用户，寻求200万天使投资...',
                author: '李教育',
                time: '3天前'
            }
        ];

        sampleResources.forEach(resource => {
            const resourceElement = this.createResourceElement(resource);
            grid.appendChild(resourceElement);
        });
    }

    /**
     * 创建资源元素
     */
    createResourceElement(resource) {
        const resourceDiv = document.createElement('div');
        resourceDiv.className = 'resource-card';
        
        const urgencyClass = resource.urgency === 'high' ? 'high' : 
                           resource.urgency === 'medium' ? 'medium' : 'low';
        
        resourceDiv.innerHTML = `
            <div class="resource-header">
                <span class="resource-type">${resource.type}</span>
                <span class="urgency ${urgencyClass}">${resource.urgency === 'high' ? '紧急' : resource.urgency === 'medium' ? '一般' : '不急'}</span>
            </div>
            <h3>${resource.title}</h3>
            <p>${resource.content}</p>
            <div class="resource-footer">
                <span class="author">by ${resource.author}</span>
                <span class="time">${resource.time}</span>
            </div>
        `;
        return resourceDiv;
    }

    /**
     * 初始化弹幕系统
     */
    initDanmaku() {
        this.danmakuMessages = [
            '💪 加油！创业路上不孤单！',
            '🚀 相信自己，一定能成功！',
            '✨ 每一次努力都是成长！',
            '🔥 坚持就是胜利！',
            '💡 创新改变世界！',
            '🌟 梦想终将实现！'
        ];
        
        this.startDanmakuAnimation();
    }

    /**
     * 开始弹幕动画
     */
    startDanmakuAnimation() {
        const display = document.querySelector('.danmaku-display');
        if (!display) return;

        setInterval(() => {
            if (this.currentTab === 'danmaku') {
                this.addRandomDanmaku();
            }
        }, 3000);
    }

    /**
     * 添加随机弹幕
     */
    addRandomDanmaku() {
        const display = document.querySelector('.danmaku-display');
        if (!display) return;

        const message = this.danmakuMessages[Math.floor(Math.random() * this.danmakuMessages.length)];
        const danmakuItem = document.createElement('div');
        danmakuItem.className = 'danmaku-item';
        danmakuItem.textContent = message;
        danmakuItem.style.top = Math.random() * 80 + 10 + '%';
        danmakuItem.style.color = this.getRandomColor();

        display.appendChild(danmakuItem);

        // 8秒后移除弹幕
        setTimeout(() => {
            if (danmakuItem.parentNode) {
                danmakuItem.parentNode.removeChild(danmakuItem);
            }
        }, 8000);
    }

    /**
     * 发送弹幕
     */
    sendDanmaku() {
        const input = document.querySelector('.danmaku-text');
        const message = input.value.trim();
        
        if (!message) {
            this.showNotification('请输入弹幕内容', 'warning');
            return;
        }

        if (message.length > 50) {
            this.showNotification('弹幕内容不能超过50个字符', 'warning');
            return;
        }

        // 添加到弹幕显示
        const display = document.querySelector('.danmaku-display');
        if (display) {
            const danmakuItem = document.createElement('div');
            danmakuItem.className = 'danmaku-item';
            danmakuItem.textContent = message;
            danmakuItem.style.top = Math.random() * 80 + 10 + '%';
            danmakuItem.style.color = '#FFD700'; // 用户发送的弹幕用金色
            danmakuItem.style.fontWeight = 'bold';

            display.appendChild(danmakuItem);

            setTimeout(() => {
                if (danmakuItem.parentNode) {
                    danmakuItem.parentNode.removeChild(danmakuItem);
                }
            }, 8000);
        }

        // 清空输入框
        input.value = '';
        this.showNotification('弹幕发送成功！', 'success');
    }

    /**
     * 刷新弹幕
     */
    refreshDanmaku() {
        const display = document.querySelector('.danmaku-display');
        if (display) {
            // 清空现有弹幕
            display.innerHTML = '';
            
            // 添加初始弹幕
            setTimeout(() => this.addRandomDanmaku(), 500);
            setTimeout(() => this.addRandomDanmaku(), 1500);
            setTimeout(() => this.addRandomDanmaku(), 2500);
        }
    }

    /**
     * 处理帖子互动
     */
    handlePostInteraction(element) {
        const action = element.textContent.includes('👍') ? 'like' : 
                      element.textContent.includes('💬') ? 'comment' : 'share';
        
        // 简单的数字增加动画
        const currentText = element.textContent;
        const number = parseInt(currentText.match(/\d+/)[0]);
        element.textContent = currentText.replace(/\d+/, number + 1);
        
        // 添加动画效果
        element.style.transform = 'scale(1.2)';
        element.style.color = '#6366f1';
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 200);

        this.showNotification(`${action === 'like' ? '点赞' : action === 'comment' ? '评论' : '分享'}成功！`, 'success');
    }

    /**
     * 获取随机颜色
     */
    getRandomColor() {
        const colors = ['#FFD700', '#FF69B4', '#00CED1', '#98FB98', '#DDA0DD', '#F0E68C'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 使用主题的通知系统
        if (typeof MainTheme !== 'undefined' && MainTheme.showNotification) {
            MainTheme.showNotification(message, type);
        } else {
            // 简单的fallback通知
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new EntrepreneurshipModule();
});
