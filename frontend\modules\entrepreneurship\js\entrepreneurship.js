/**
 * 创业模块JavaScript功能
 */

class EntrepreneurshipModule {
    constructor() {
        this.currentTab = 'entrepreneurs';
        this.isLoggedIn = false;
        this.navbar = null;
        this.init();

        // 暴露到全局供页面脚本调用
        window.entrepreneurshipModule = this;
    }

    async init() {
        // 初始化导航栏
        this.navbar = new NavbarComponent({ currentPage: 'entrepreneurship' });

        // 等待导航栏异步初始化完成
        await this.navbar.initAsync();

        // 获取登录状态
        this.isLoggedIn = this.navbar.getLoginStatus();
        this.currentUser = this.navbar.getCurrentUser();

        this.bindEvents();
        this.initDanmaku();
        await this.loadEntrepreneurs();
    }

    /**
     * 刷新登录状态
     */
    async refreshLoginStatus() {
        await this.navbar.refreshLoginStatus();
        this.isLoggedIn = this.navbar.getLoginStatus();
        this.currentUser = this.navbar.getCurrentUser();

        // 重新加载创业者数据以更新界面
        await this.loadEntrepreneurs();
    }



    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 标签页切换
        const tabBtns = document.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => {
            btn.addEventListener('click', async (e) => {
                await this.switchTab(e.target.dataset.tab);
            });
        });

        // 关注按钮
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('follow-btn')) {
                this.toggleFollow(e.target);
            }
        });

        // 弹幕发送
        const sendBtn = document.querySelector('.send-danmaku-btn');
        const danmakuInput = document.querySelector('.danmaku-text');
        
        if (sendBtn) {
            sendBtn.addEventListener('click', () => {
                this.sendDanmaku();
            });
        }

        if (danmakuInput) {
            danmakuInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendDanmaku();
                }
            });
        }

        // 帖子互动
        document.addEventListener('click', (e) => {
            if (e.target.closest('.post-stats span')) {
                this.handlePostInteraction(e.target.closest('.post-stats span'));
            }
        });

        // 联系方式复制
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('contact-info') && e.target.classList.contains('visible')) {
                this.copyContactInfo(e.target);
            }
        });

        // 可见性控制
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('visibility-btn')) {
                this.toggleVisibility(e.target);
            }
        });
    }

    /**
     * 切换标签页
     */
    async switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // 根据不同标签页加载对应内容
        switch (tabName) {
            case 'entrepreneurs':
                await this.loadEntrepreneurs();
                break;
            case 'posts':
                this.loadPosts();
                break;
            case 'resources':
                this.loadResources();
                break;
            case 'danmaku':
                this.refreshDanmaku();
                break;
        }
    }

    /**
     * 切换关注状态
     */
    async toggleFollow(btn) {
        // 检查登录状态
        if (!this.isLoggedIn) {
            this.showNotification('请先登录后再进行关注操作', 'warning');
            // 立即跳转到登录页面
            window.location.href = 'login.html';
            return;
        }

        const userId = btn.dataset.userId;

        // 根据按钮类型判断操作
        let action = 'follow';
        if (btn.classList.contains('following') || btn.classList.contains('mutual-follow')) {
            action = 'unfollow';
        }

        // 禁用按钮防止重复点击
        btn.disabled = true;

        try {
            const token = localStorage.getItem('userToken');

            const response = await fetch(NavbarComponent.calculateApiPath('user/follow.php'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    action: action,
                    user_id: parseInt(userId),
                    token: token
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message || '操作成功', 'success');
                // 重新加载用户列表以更新状态
                await this.loadEntrepreneurs();
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            this.showNotification(error.message || '操作失败，请重试', 'error');
        } finally {
            btn.disabled = false;
        }
    }

    /**
     * 显示联系方式
     */
    async showContactInfo(contactElement, userId) {
        if (contactElement) {
            contactElement.classList.remove('hidden');
            contactElement.classList.add('visible');

            try {
                // 从API获取用户的真实联系方式
                const response = await fetch(`../../../../backend/api/user/contact.php?id=${userId}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('userToken')}`
                    }
                });

                const result = await response.json();

                if (result.success && result.data.contact_type && result.data.contact_value) {
                    const contactTypeMap = {
                        'phone': '电话',
                        'qq': 'QQ',
                        'wechat': '微信'
                    };
                    const contactType = contactTypeMap[result.data.contact_type] || result.data.contact_type;
                    contactElement.textContent = `${contactType}: ${result.data.contact_value}`;
                } else {
                    contactElement.textContent = '暂未提供联系方式';
                }
            } catch (error) {
                contactElement.textContent = '获取联系方式失败';
            }
        }
    }

    /**
     * 隐藏联系方式
     */
    hideContactInfo(contactElement) {
        if (contactElement) {
            contactElement.classList.remove('visible');
            contactElement.classList.add('hidden');
            contactElement.textContent = '★ ★ ★ ★ ★ ★ ★';
        }
    }

    /**
     * 复制联系方式
     */
    copyContactInfo(contactElement) {
        const contactText = contactElement.textContent.trim();

        // 检查是否为"暂未设置联系方式"或星号，不允许复制
        if (contactText === '暂未设置联系方式' || contactText.includes('★')) {
            this.showNotification('暂无可复制的联系方式', 'warning');
            return;
        }

        // 提取联系方式值（去掉前缀如"电话："、"QQ："、"微信："）
        let copyText = contactText;
        const prefixes = ['电话：', 'QQ：', '微信：', '电话:', 'QQ:', '微信:'];
        for (const prefix of prefixes) {
            if (contactText.startsWith(prefix)) {
                copyText = contactText.substring(prefix.length).trim();
                break;
            }
        }

        // 尝试使用现代的 Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(copyText).then(() => {
                this.showNotification('联系方式已复制到剪贴板', 'success');
                this.showCopyAnimation(contactElement);
            }).catch(() => {
                this.fallbackCopyTextToClipboard(copyText, contactElement);
            });
        } else {
            // 降级方案
            this.fallbackCopyTextToClipboard(copyText, contactElement);
        }
    }

    /**
     * 降级复制方案
     */
    fallbackCopyTextToClipboard(text, element) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand('copy');
            this.showNotification('联系方式已复制到剪贴板', 'success');
            this.showCopyAnimation(element);
        } catch (err) {
            this.showNotification('复制失败，请手动复制', 'warning');
        }

        document.body.removeChild(textArea);
    }

    /**
     * 显示复制动画
     */
    showCopyAnimation(element) {
        const originalTransform = element.style.transform;
        element.style.transform = 'scale(1.1)';
        element.style.background = 'linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(16, 185, 129, 0.2))';

        setTimeout(() => {
            element.style.transform = originalTransform;
            element.style.background = '';
        }, 200);
    }

    /**
     * 加载创业者数据
     */
    async loadEntrepreneurs() {
        const grid = document.querySelector('.entrepreneurs-grid');
        if (!grid) return;

        try {
            // 显示加载状态
            grid.style.opacity = '0.5';

            // 从API获取用户数据
            const token = localStorage.getItem('userToken');
            const headers = {
                'Content-Type': 'application/json'
            };

            // 如果已登录，添加认证头
            if (token && this.isLoggedIn) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            const response = await fetch(NavbarComponent.calculateApiPath('user/list.php'), {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({ token: token })
            });
            const result = await response.json();

            if (result.success) {
                // 清空现有内容
                grid.innerHTML = '';

                // 渲染用户卡片
                result.data.users.forEach((user, index) => {
                    // 判断是否为当前用户：已登录且用户ID匹配
                    const isCurrentUser = this.isLoggedIn && this.currentUser &&
                                         parseInt(user.id) === parseInt(this.currentUser.user_id);
                    const userCard = this.createEntrepreneurCard(user, isCurrentUser);
                    grid.appendChild(userCard);
                });

                grid.style.opacity = '1';
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            console.error('加载创业者数据失败:', error);
            grid.style.opacity = '1';
            this.showNotification('加载创业者数据失败', 'error');
        }
    }



    /**
     * 创建创业者卡片元素
     */
    createEntrepreneurCard(user, isCurrentUser = false) {
        const cardDiv = document.createElement('div');
        cardDiv.className = 'entrepreneur-card';
        cardDiv.dataset.userId = user.id;

        if (isCurrentUser) {
            cardDiv.classList.add('current-user-card');
        }

        // 处理标签数据
        const tags = Array.isArray(user.tags) ? user.tags : (user.tags ? user.tags.split(',') : []);

        // 处理联系方式显示
        let contactInfoClass = 'contact-info hidden';
        let contactInfoText = '★ ★ ★ ★ ★ ★ ★';

        if (isCurrentUser) {
            // 当前用户显示自己的联系方式
            contactInfoClass = 'contact-info visible';
            contactInfoText = user.contact_type && user.contact_value ?
                `${this.getContactTypeLabel(user.contact_type)}: ${user.contact_value}` :
                '暂未设置联系方式';
        } else if (user.is_mutual_follow) {
            // 互相关注的用户可以查看联系方式
            contactInfoClass = 'contact-info visible';
            contactInfoText = user.contact_type && user.contact_value ?
                `${this.getContactTypeLabel(user.contact_type)}: ${user.contact_value}` :
                '暂未设置联系方式';
        } else if (user.is_followed_by && !user.is_following) {
            // 对方关注了我，但我没关注对方
            contactInfoText = '对方已关注你，回关后可查看联系方式';
        }

        // 处理空值显示
        const mainBusiness = user.main_business || '暂未填写';
        const sideBusiness = user.side_business || '暂未填写';
        const location = user.location || '未知';
        const bio = user.bio || '创业者';
        const description = user.description || '';

        // 右上角按钮
        let actionButton = '';
        if (isCurrentUser) {
            // 确保is_visible有正确的值，默认为1（可见）
            const isVisible = user.is_visible !== undefined ? user.is_visible : 1;
            const visibilityText = isVisible == 1 ? '隐藏' : '显示';
            actionButton = `
                <div class="visibility-control">
                    <button class="visibility-btn" data-visible="${isVisible}">
                        ${visibilityText}
                    </button>
                    <div class="help-tooltip">
                        <span class="help-icon">?</span>
                        <div class="tooltip-content">控制其他人是否能在创业者列表中看到你</div>
                    </div>
                </div>
            `;
        } else {
            // 其他用户的关注按钮
            let followBtnText = '关注';
            let followBtnClass = 'follow-btn';

            if (user.is_mutual_follow) {
                followBtnText = '互相关注';
                followBtnClass = 'follow-btn mutual-follow';
            } else if (user.is_following) {
                followBtnText = '已关注';
                followBtnClass = 'follow-btn following';
            } else if (user.is_followed_by) {
                followBtnText = '回关';
                followBtnClass = 'follow-btn follow-back';
            }

            actionButton = `<button class="${followBtnClass}" data-user-id="${user.id}">${followBtnText}</button>`;
        }

        cardDiv.innerHTML = `
            <div class="card-header">
                <div class="user-info">
                    <h3>${user.nickname}${isCurrentUser ? ' (我)' : ''}</h3>
                    <p class="title">${bio}</p>
                    <div class="location">📍 ${location}</div>
                </div>
                ${actionButton}
            </div>
            <div class="card-body">
                <div class="business-info">
                    <div class="business-item">
                        <span class="label">主业</span>
                        <span class="value">${mainBusiness}</span>
                    </div>
                    <div class="business-item">
                        <span class="label">副业</span>
                        <span class="value">${sideBusiness}</span>
                    </div>
                    <div class="business-item">
                        <span class="label">联系方式</span>
                        <span class="value ${contactInfoClass}">${contactInfoText}</span>
                    </div>
                </div>
                <div class="tags">
                    ${tags.map(tag => `<span class="tag">${tag.trim()}</span>`).join('')}
                </div>
                ${description ? `
                <div class="description">
                    <h4>详细介绍</h4>
                    <p>${description}</p>
                </div>
                ` : ''}
            </div>
            <div class="card-footer">
                <div class="stats">
                    <span>👥 ${user.followers_count || 0} 关注者</span>
                    <span>📝 ${user.posts_count || 0} 动态</span>
                </div>
            </div>
        `;

        return cardDiv;
    }

    /**
     * 获取联系方式类型标签
     */
    getContactTypeLabel(type) {
        const typeMap = {
            'phone': '电话',
            'qq': 'QQ',
            'wechat': '微信'
        };
        return typeMap[type] || type;
    }

    /**
     * 切换可见性
     */
    async toggleVisibility(btn) {
        if (!this.isLoggedIn) {
            this.showNotification('请先登录', 'warning');
            return;
        }

        const isVisible = btn.dataset.visible === '1';
        btn.disabled = true;

        try {
            const token = localStorage.getItem('userToken');
            const response = await fetch(NavbarComponent.calculateApiPath('user/visibility.php'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({ token: token })
            });

            const result = await response.json();

            if (result.success) {
                const newVisibility = result.data.is_visible;
                btn.dataset.visible = newVisibility ? '1' : '0';
                btn.textContent = newVisibility ? '隐藏' : '显示';
                this.showNotification(result.message, 'success');

                // 重新加载创业者列表以同步界面状态
                await this.loadEntrepreneurs();
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            this.showNotification(error.message || '操作失败，请重试', 'error');
        } finally {
            btn.disabled = false;
        }
    }

    /**
     * 加载帖子数据
     */
    loadPosts() {
        console.log('加载社区动态...');
        
        // 模拟数据加载
        const container = document.querySelector('.posts-container');
        if (container) {
            // 可以在这里动态添加更多帖子
            this.addSamplePosts(container);
        }
    }

    /**
     * 添加示例帖子
     */
    addSamplePosts(container) {
        const samplePosts = [
            {
                author: '李电商',
                avatar: 'https://via.placeholder.com/40x40',
                time: '5小时前',
                category: '资源分享',
                title: '电商运营必备工具清单',
                content: '整理了一份电商运营的工具清单，包括数据分析、客服、营销等各个方面...',
                stats: { likes: 89, comments: 15, shares: 23 }
            },
            {
                author: '王技术',
                avatar: 'https://via.placeholder.com/40x40',
                time: '1天前',
                category: '技术分享',
                title: '如何快速搭建MVP产品',
                content: '分享一下我们团队快速搭建MVP的经验，从需求分析到产品上线只用了2周...',
                stats: { likes: 156, comments: 32, shares: 45 }
            }
        ];

        // 检查是否已经添加过示例帖子
        if (container.children.length <= 1) {
            samplePosts.forEach(post => {
                const postElement = this.createPostElement(post);
                container.appendChild(postElement);
            });
        }
    }

    /**
     * 创建帖子元素
     */
    createPostElement(post) {
        const postDiv = document.createElement('div');
        postDiv.className = 'post-card';
        postDiv.innerHTML = `
            <div class="post-header">
                <div class="author-info">
                    <img src="${post.avatar}" alt="作者头像" class="author-avatar">
                    <div>
                        <h4>${post.author}</h4>
                        <span class="post-time">${post.time}</span>
                    </div>
                </div>
                <span class="post-category">${post.category}</span>
            </div>
            <div class="post-content">
                <h3>${post.title}</h3>
                <p>${post.content}</p>
            </div>
            <div class="post-footer">
                <div class="post-stats">
                    <span>👍 ${post.stats.likes}</span>
                    <span>💬 ${post.stats.comments}</span>
                    <span>🔄 ${post.stats.shares}</span>
                </div>
            </div>
        `;
        return postDiv;
    }

    /**
     * 加载资源合作数据
     */
    loadResources() {
        console.log('加载资源合作信息...');
        
        // 这里可以添加更多资源合作卡片
        const grid = document.querySelector('.resources-grid');
        if (grid && grid.children.length <= 1) {
            this.addSampleResources(grid);
        }
    }

    /**
     * 添加示例资源
     */
    addSampleResources(grid) {
        const sampleResources = [
            {
                type: '提供资源',
                urgency: 'medium',
                title: '免费提供办公场地',
                content: '我们公司有多余的办公位置，可以免费提供给初创团队使用...',
                author: '张场地',
                time: '2天前'
            },
            {
                type: '寻求投资',
                urgency: 'high',
                title: '教育科技项目寻求天使投资',
                content: '我们的在线教育平台已有10万用户，寻求200万天使投资...',
                author: '李教育',
                time: '3天前'
            }
        ];

        sampleResources.forEach(resource => {
            const resourceElement = this.createResourceElement(resource);
            grid.appendChild(resourceElement);
        });
    }

    /**
     * 创建资源元素
     */
    createResourceElement(resource) {
        const resourceDiv = document.createElement('div');
        resourceDiv.className = 'resource-card';
        
        const urgencyClass = resource.urgency === 'high' ? 'high' : 
                           resource.urgency === 'medium' ? 'medium' : 'low';
        
        resourceDiv.innerHTML = `
            <div class="resource-header">
                <span class="resource-type">${resource.type}</span>
                <span class="urgency ${urgencyClass}">${resource.urgency === 'high' ? '紧急' : resource.urgency === 'medium' ? '一般' : '不急'}</span>
            </div>
            <h3>${resource.title}</h3>
            <p>${resource.content}</p>
            <div class="resource-footer">
                <span class="author">by ${resource.author}</span>
                <span class="time">${resource.time}</span>
            </div>
        `;
        return resourceDiv;
    }

    /**
     * 初始化弹幕系统
     */
    initDanmaku() {
        this.danmakuMessages = [
            '💪 加油！创业路上不孤单！',
            '🚀 相信自己，一定能成功！',
            '✨ 每一次努力都是成长！',
            '🔥 坚持就是胜利！',
            '💡 创新改变世界！',
            '🌟 梦想终将实现！'
        ];
        
        this.startDanmakuAnimation();
    }

    /**
     * 开始弹幕动画
     */
    startDanmakuAnimation() {
        const display = document.querySelector('.danmaku-display');
        if (!display) return;

        setInterval(() => {
            if (this.currentTab === 'danmaku') {
                this.addRandomDanmaku();
            }
        }, 3000);
    }

    /**
     * 添加随机弹幕
     */
    addRandomDanmaku() {
        const display = document.querySelector('.danmaku-display');
        if (!display) return;

        const message = this.danmakuMessages[Math.floor(Math.random() * this.danmakuMessages.length)];
        const danmakuItem = document.createElement('div');
        danmakuItem.className = 'danmaku-item';
        danmakuItem.textContent = message;
        danmakuItem.style.top = Math.random() * 80 + 10 + '%';
        danmakuItem.style.color = this.getRandomColor();

        display.appendChild(danmakuItem);

        // 8秒后移除弹幕
        setTimeout(() => {
            if (danmakuItem.parentNode) {
                danmakuItem.parentNode.removeChild(danmakuItem);
            }
        }, 8000);
    }

    /**
     * 发送弹幕
     */
    sendDanmaku() {
        const input = document.querySelector('.danmaku-text');
        const message = input.value.trim();
        
        if (!message) {
            this.showNotification('请输入弹幕内容', 'warning');
            return;
        }

        if (message.length > 50) {
            this.showNotification('弹幕内容不能超过50个字符', 'warning');
            return;
        }

        // 添加到弹幕显示
        const display = document.querySelector('.danmaku-display');
        if (display) {
            const danmakuItem = document.createElement('div');
            danmakuItem.className = 'danmaku-item';
            danmakuItem.textContent = message;
            danmakuItem.style.top = Math.random() * 80 + 10 + '%';
            danmakuItem.style.color = '#FFD700'; // 用户发送的弹幕用金色
            danmakuItem.style.fontWeight = 'bold';

            display.appendChild(danmakuItem);

            setTimeout(() => {
                if (danmakuItem.parentNode) {
                    danmakuItem.parentNode.removeChild(danmakuItem);
                }
            }, 8000);
        }

        // 清空输入框
        input.value = '';
        this.showNotification('弹幕发送成功！', 'success');
    }

    /**
     * 刷新弹幕
     */
    refreshDanmaku() {
        const display = document.querySelector('.danmaku-display');
        if (display) {
            // 清空现有弹幕
            display.innerHTML = '';
            
            // 添加初始弹幕
            setTimeout(() => this.addRandomDanmaku(), 500);
            setTimeout(() => this.addRandomDanmaku(), 1500);
            setTimeout(() => this.addRandomDanmaku(), 2500);
        }
    }

    /**
     * 处理帖子互动
     */
    handlePostInteraction(element) {
        const action = element.textContent.includes('👍') ? 'like' : 
                      element.textContent.includes('💬') ? 'comment' : 'share';
        
        // 简单的数字增加动画
        const currentText = element.textContent;
        const number = parseInt(currentText.match(/\d+/)[0]);
        element.textContent = currentText.replace(/\d+/, number + 1);
        
        // 添加动画效果
        element.style.transform = 'scale(1.2)';
        element.style.color = '#6366f1';
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 200);

        this.showNotification(`${action === 'like' ? '点赞' : action === 'comment' ? '评论' : '分享'}成功！`, 'success');
    }

    /**
     * 获取随机颜色
     */
    getRandomColor() {
        const colors = ['#FFD700', '#FF69B4', '#00CED1', '#98FB98', '#DDA0DD', '#F0E68C'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 使用主题的通知系统
        if (typeof MainTheme !== 'undefined' && MainTheme.showNotification) {
            MainTheme.showNotification(message, type);
        } else {
            // 简单的fallback通知
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new EntrepreneurshipModule();
});
