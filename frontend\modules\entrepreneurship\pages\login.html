<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - NEXUS 创业者社区</title>
    <link rel="stylesheet" href="../../../shared/css/main-theme.css">
    <link rel="stylesheet" href="../css/entrepreneurship.css">
    <link rel="stylesheet" href="../css/auth.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container flex justify-between items-center">
            <div class="text-2xl font-bold" style="color: var(--white); font-family: var(--font-display);">
                <a href="../../../../index.html" style="color: inherit; text-decoration: none;">NEXUS</a>
            </div>
            <div class="flex gap-8">
                <a href="index.html" class="nav-item">返回社区</a>
                <a href="register.html" class="nav-item">注册账号</a>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <!-- 导航栏将通过JavaScript动态插入 -->

    <div class="auth-container">
        <div class="auth-card">
            <!-- 标题区域 -->
            <div class="auth-header">
                <div class="auth-icon">
                    <span>🚀</span>
                </div>
                <h1>欢迎回来</h1>
                <p>登录您的创业者账号，继续您的创业之旅</p>
            </div>

            <!-- 登录表单 -->
            <form class="auth-form" id="loginForm">
                <div class="form-group">
                    <label class="form-label" for="username">
                        <span class="label-text">账号</span>
                        <span class="required">*</span>
                    </label>
                    <div class="input-wrapper">
                        <span class="input-icon">👤</span>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="form-input" 
                            placeholder="请输入您的账号"
                            required
                        >
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">
                        <span class="label-text">密码</span>
                        <span class="required">*</span>
                    </label>
                    <div class="input-wrapper">
                        <span class="input-icon">🔒</span>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="请输入您的密码"
                            required
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <span class="toggle-icon">👁️</span>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-wrapper">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        <span class="checkmark"></span>
                        <span class="checkbox-text">记住我</span>
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>

                <button type="submit" class="btn btn-primary btn-lg auth-submit">
                    <span class="btn-text">登录</span>
                    <span class="btn-loading" style="display: none;">
                        <span class="loading-spinner"></span>
                        登录中...
                    </span>
                </button>
            </form>

            <!-- 底部链接 -->
            <div class="auth-footer">
                <p>还没有账号？ <a href="register.html" class="auth-link">立即注册</a></p>
            </div>
        </div>

        <!-- 装饰元素 -->
        <div class="auth-decoration">
            <div class="decoration-item decoration-1"></div>
            <div class="decoration-item decoration-2"></div>
            <div class="decoration-item decoration-3"></div>
        </div>
    </div>

    <!-- 底部信息 -->
    <footer class="auth-page-footer">
        <div class="container">
            <p>&copy; 2024 NEXUS 创业者社区 · 连接创业者，共创未来</p>
        </div>
    </footer>

    <script src="../../../shared/js/main-theme.js"></script>
    <script src="../js/auth.js"></script>
</body>
</html>
