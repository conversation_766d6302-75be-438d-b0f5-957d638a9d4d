<?php
/**
 * 更新用户信息API
 * 创业者社区平台
 */

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit();
}

require_once __DIR__ . '/../../models/user/User.php';

try {
    // 获取当前用户ID
    $currentUserId = getCurrentUserId();
    if (!$currentUserId) {
        throw new Exception('请先登录');
    }

    // 获取POST数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        throw new Exception('无效的请求数据');
    }

    // 验证必填字段
    if (empty($input['nickname'])) {
        throw new Exception('昵称不能为空');
    }

    // 验证字段长度
    $fieldValidations = [
        ['field' => 'nickname', 'value' => $input['nickname'], 'maxLength' => 100, 'name' => '昵称'],
        ['field' => 'bio', 'value' => $input['bio'] ?? '', 'maxLength' => 255, 'name' => '个人标签'],
        ['field' => 'mainBusiness', 'value' => $input['mainBusiness'] ?? '', 'maxLength' => 255, 'name' => '主业'],
        ['field' => 'sideBusiness', 'value' => $input['sideBusiness'] ?? '', 'maxLength' => 255, 'name' => '副业'],
        ['field' => 'location', 'value' => $input['location'] ?? '', 'maxLength' => 100, 'name' => '所在地'],
        ['field' => 'contactValue', 'value' => $input['contactValue'] ?? '', 'maxLength' => 100, 'name' => '联系方式'],
        ['field' => 'description', 'value' => $input['description'] ?? '', 'maxLength' => 1000, 'name' => '详细描述']
    ];

    foreach ($fieldValidations as $validation) {
        if (!empty($validation['value']) && mb_strlen($validation['value']) > $validation['maxLength']) {
            throw new Exception($validation['name'] . '不能超过' . $validation['maxLength'] . '个字符');
        }
    }

    $userModel = new User();
    
    // 检查用户是否存在
    $user = $userModel->findById($currentUserId);
    if (!$user) {
        throw new Exception('用户不存在');
    }

    // 准备更新数据
    $updateData = [
        'nickname' => trim($input['nickname']),
        'bio' => !empty($input['bio']) ? trim($input['bio']) : null,
        'main_business' => !empty($input['mainBusiness']) ? trim($input['mainBusiness']) : null,
        'side_business' => !empty($input['sideBusiness']) ? trim($input['sideBusiness']) : null,
        'location' => !empty($input['location']) ? trim($input['location']) : null,
        'description' => !empty($input['description']) ? trim($input['description']) : null,
        'contact_type' => !empty($input['contactType']) ? $input['contactType'] : null,
        'contact_value' => !empty($input['contactValue']) ? trim($input['contactValue']) : null
    ];

    // 执行更新
    $result = $userModel->updateProfile($currentUserId, $updateData);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => '个人信息更新成功'
        ]);
    } else {
        throw new Exception('更新失败，请重试');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 获取当前用户ID
 */
function getCurrentUserId()
{
    // 从Authorization头获取token
    $headers = getallheaders();
    if (!isset($headers['Authorization'])) {
        return null;
    }

    $token = str_replace('Bearer ', '', $headers['Authorization']);
    
    try {
        $payload = json_decode(base64_decode($token), true);
        if (!$payload || !isset($payload['user_id'])) {
            return null;
        }

        // 检查token是否过期
        if (time() - $payload['timestamp'] > 24 * 60 * 60) {
            return null;
        }

        return $payload['user_id'];
    } catch (Exception $e) {
        return null;
    }
}
?>
