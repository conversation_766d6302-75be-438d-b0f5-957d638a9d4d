<?php
/**
 * 修改密码API
 * 创业者社区平台
 */

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ]);
    exit();
}

require_once __DIR__ . '/../../models/user/User.php';

try {
    // 获取当前用户ID
    $currentUserId = getCurrentUserId();
    if (!$currentUserId) {
        throw new Exception('请先登录');
    }

    // 获取POST数据
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        throw new Exception('无效的请求数据');
    }

    // 验证必填字段
    if (empty($input['currentPassword']) || empty($input['newPassword'])) {
        throw new Exception('当前密码和新密码不能为空');
    }

    // 验证新密码长度
    if (strlen($input['newPassword']) < 6 || strlen($input['newPassword']) > 50) {
        throw new Exception('新密码长度必须在6-50位之间');
    }

    $userModel = new User();
    
    // 获取用户信息
    $user = $userModel->findById($currentUserId);
    if (!$user) {
        throw new Exception('用户不存在');
    }

    // 验证当前密码
    if (!$userModel->verifyPassword($input['currentPassword'], $user['password'])) {
        throw new Exception('当前密码错误');
    }

    // 更新密码
    $result = $userModel->updatePassword($currentUserId, $input['newPassword']);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => '密码修改成功'
        ]);
    } else {
        throw new Exception('密码修改失败，请重试');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 获取当前用户ID
 */
function getCurrentUserId()
{
    // 从Authorization头获取token
    $headers = getallheaders();
    if (!isset($headers['Authorization'])) {
        return null;
    }

    $token = str_replace('Bearer ', '', $headers['Authorization']);
    
    try {
        $payload = json_decode(base64_decode($token), true);
        if (!$payload || !isset($payload['user_id'])) {
            return null;
        }

        // 检查token是否过期
        if (time() - $payload['timestamp'] > 24 * 60 * 60) {
            return null;
        }

        return $payload['user_id'];
    } catch (Exception $e) {
        return null;
    }
}
?>
