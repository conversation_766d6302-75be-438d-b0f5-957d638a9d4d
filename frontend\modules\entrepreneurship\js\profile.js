/**
 * 个人主页模块
 * 创业者社区平台
 */

class ProfileModule {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    /**
     * 初始化
     */
    async init() {
        // 检查登录状态
        await this.checkLoginStatus();
        
        // 绑定事件
        this.bindEvents();
        
        // 初始化字符计数
        this.initCharCounters();
        
        // 加载用户信息
        await this.loadUserProfile();
    }

    /**
     * 检查登录状态
     */
    async checkLoginStatus() {
        const token = localStorage.getItem('userToken');
        if (!token) {
            this.showNotification('请先登录', 'warning');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1500);
            return;
        }

        try {
            const response = await fetch('../../../../backend/api/auth/verify.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();
            if (!result.success) {
                throw new Error('登录状态已过期');
            }

            this.currentUser = result.data;
        } catch (error) {
            this.showNotification('登录状态已过期，请重新登录', 'warning');
            localStorage.removeItem('userToken');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1500);
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 个人信息表单提交
        const profileForm = document.getElementById('profileForm');
        profileForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleProfileUpdate(profileForm);
        });

        // 修改密码按钮
        const changePasswordBtn = document.getElementById('changePasswordBtn');
        changePasswordBtn.addEventListener('click', () => {
            this.showPasswordModal();
        });

        // 密码表单提交
        const passwordForm = document.getElementById('passwordForm');
        passwordForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePasswordChange(passwordForm);
        });

        // 模态框关闭
        const closePasswordModal = document.getElementById('closePasswordModal');
        const cancelPasswordChange = document.getElementById('cancelPasswordChange');
        const passwordModal = document.getElementById('passwordModal');

        closePasswordModal.addEventListener('click', () => {
            this.hidePasswordModal();
        });

        cancelPasswordChange.addEventListener('click', () => {
            this.hidePasswordModal();
        });

        passwordModal.addEventListener('click', (e) => {
            if (e.target === passwordModal) {
                this.hidePasswordModal();
            }
        });

        // 联系方式类型选择
        const contactType = document.getElementById('contactType');
        const contactValue = document.getElementById('contactValue');
        
        contactType.addEventListener('change', () => {
            if (contactType.value) {
                contactValue.disabled = false;
                contactValue.placeholder = this.getContactPlaceholder(contactType.value);
            } else {
                contactValue.disabled = true;
                contactValue.placeholder = '请先选择联系方式类型';
                contactValue.value = '';
            }
        });

        // 退出登录
        const logoutBtn = document.getElementById('logoutBtn');
        logoutBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleLogout();
        });
    }

    /**
     * 初始化字符计数
     */
    initCharCounters() {
        const fieldConfigs = [
            { id: 'nickname', maxLength: 100, warningAt: 80 },
            { id: 'bio', maxLength: 255, warningAt: 200 },
            { id: 'mainBusiness', maxLength: 255, warningAt: 200 },
            { id: 'sideBusiness', maxLength: 255, warningAt: 200 },
            { id: 'location', maxLength: 100, warningAt: 80 },
            { id: 'contactValue', maxLength: 100, warningAt: 80 },
            { id: 'description', maxLength: 1000, warningAt: 600, dangerAt: 800 },
            { id: 'newPassword', maxLength: 50, warningAt: 40 },
            { id: 'confirmNewPassword', maxLength: 50, warningAt: 40 }
        ];

        fieldConfigs.forEach(config => {
            const field = document.getElementById(config.id);
            const counter = document.getElementById(config.id + 'Counter');
            
            if (field && counter) {
                field.addEventListener('input', (e) => {
                    const length = e.target.value.length;
                    counter.textContent = length;
                    
                    const counterElement = counter.parentElement;
                    counterElement.classList.remove('warning', 'danger');
                    
                    if (config.dangerAt && length > config.dangerAt) {
                        counterElement.classList.add('danger');
                    } else if (length > config.warningAt) {
                        counterElement.classList.add('warning');
                    }
                });
            }
        });
    }

    /**
     * 加载用户信息
     */
    async loadUserProfile() {
        try {
            const token = localStorage.getItem('userToken');
            const response = await fetch('../../../../backend/api/user/profile.php', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();
            if (result.success) {
                this.fillUserData(result.data);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            this.showNotification('加载用户信息失败: ' + error.message, 'error');
        }
    }

    /**
     * 填充用户数据到表单
     */
    fillUserData(userData) {
        // 基本信息
        document.getElementById('nickname').value = userData.nickname || '';
        document.getElementById('username').value = userData.username || '';
        
        // 个人资料
        document.getElementById('bio').value = userData.bio || '';
        document.getElementById('mainBusiness').value = userData.main_business || '';
        document.getElementById('sideBusiness').value = userData.side_business || '';
        document.getElementById('location').value = userData.location || '';
        document.getElementById('description').value = userData.description || '';
        
        // 联系方式
        const contactType = document.getElementById('contactType');
        const contactValue = document.getElementById('contactValue');
        
        if (userData.contact_type) {
            contactType.value = userData.contact_type;
            contactValue.disabled = false;
            contactValue.placeholder = this.getContactPlaceholder(userData.contact_type);
            contactValue.value = userData.contact_value || '';
        }

        // 触发字符计数更新
        this.updateAllCounters();
    }

    /**
     * 更新所有字符计数器
     */
    updateAllCounters() {
        const fields = ['nickname', 'bio', 'mainBusiness', 'sideBusiness', 'location', 'contactValue', 'description'];
        fields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            const counter = document.getElementById(fieldId + 'Counter');
            if (field && counter) {
                const event = new Event('input');
                field.dispatchEvent(event);
            }
        });
    }

    /**
     * 获取联系方式占位符
     */
    getContactPlaceholder(type) {
        const placeholders = {
            'phone': '请输入手机号码',
            'qq': '请输入QQ号码',
            'wechat': '请输入微信号'
        };
        return placeholders[type] || '请输入联系方式';
    }

    /**
     * 处理个人信息更新
     */
    async handleProfileUpdate(form) {
        const formData = new FormData(form);
        
        // 验证必填字段
        if (!formData.get('nickname')) {
            this.showNotification('昵称不能为空', 'error');
            return;
        }

        // 验证字段长度
        const fieldValidations = [
            { value: formData.get('nickname'), maxLength: 100, name: '昵称' },
            { value: formData.get('bio'), maxLength: 255, name: '个人标签' },
            { value: formData.get('mainBusiness'), maxLength: 255, name: '主业' },
            { value: formData.get('sideBusiness'), maxLength: 255, name: '副业' },
            { value: formData.get('location'), maxLength: 100, name: '所在地' },
            { value: formData.get('contactValue'), maxLength: 100, name: '联系方式' },
            { value: formData.get('description'), maxLength: 1000, name: '详细描述' }
        ];

        for (const validation of fieldValidations) {
            if (validation.value && validation.value.length > validation.maxLength) {
                this.showNotification(`${validation.name}不能超过${validation.maxLength}个字符`, 'error');
                return;
            }
        }

        // 显示加载状态
        this.showLoading(form);

        try {
            const updateData = {
                nickname: formData.get('nickname'),
                bio: formData.get('bio'),
                mainBusiness: formData.get('mainBusiness'),
                sideBusiness: formData.get('sideBusiness'),
                location: formData.get('location'),
                description: formData.get('description'),
                contactType: formData.get('contactType'),
                contactValue: formData.get('contactValue')
            };

            const token = localStorage.getItem('userToken');
            const response = await fetch('../../../../backend/api/user/update.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(updateData)
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('个人信息更新成功！', 'success');
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            this.showNotification(error.message || '更新失败，请重试', 'error');
        } finally {
            this.hideLoading(form);
        }
    }

    /**
     * 显示密码修改模态框
     */
    showPasswordModal() {
        const modal = document.getElementById('passwordModal');
        modal.style.display = 'flex';

        // 清空表单
        const form = document.getElementById('passwordForm');
        form.reset();
        this.updatePasswordCounters();
    }

    /**
     * 隐藏密码修改模态框
     */
    hidePasswordModal() {
        const modal = document.getElementById('passwordModal');
        modal.style.display = 'none';
    }

    /**
     * 更新密码字符计数器
     */
    updatePasswordCounters() {
        const newPasswordCounter = document.getElementById('newPasswordCounter');
        const confirmNewPasswordCounter = document.getElementById('confirmNewPasswordCounter');

        if (newPasswordCounter) newPasswordCounter.textContent = '0';
        if (confirmNewPasswordCounter) confirmNewPasswordCounter.textContent = '0';
    }

    /**
     * 处理密码修改
     */
    async handlePasswordChange(form) {
        const formData = new FormData(form);

        const currentPassword = formData.get('currentPassword');
        const newPassword = formData.get('newPassword');
        const confirmNewPassword = formData.get('confirmNewPassword');

        // 验证必填字段
        if (!currentPassword || !newPassword || !confirmNewPassword) {
            this.showNotification('请填写所有密码字段', 'error');
            return;
        }

        // 验证新密码长度
        if (newPassword.length < 6 || newPassword.length > 50) {
            this.showNotification('新密码长度必须在6-50位之间', 'error');
            return;
        }

        // 验证密码确认
        if (newPassword !== confirmNewPassword) {
            this.showNotification('两次输入的新密码不一致', 'error');
            return;
        }

        // 显示加载状态
        this.showLoading(form);

        try {
            const token = localStorage.getItem('userToken');
            const response = await fetch('../../../../backend/api/user/change-password.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    currentPassword: currentPassword,
                    newPassword: newPassword
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification('密码修改成功！', 'success');
                this.hidePasswordModal();
            } else {
                throw new Error(result.message);
            }

        } catch (error) {
            this.showNotification(error.message || '密码修改失败，请重试', 'error');
        } finally {
            this.hideLoading(form);
        }
    }

    /**
     * 处理退出登录
     */
    async handleLogout() {
        try {
            const token = localStorage.getItem('userToken');

            // 调用后端退出API
            await fetch('../../../../backend/api/auth/logout.php', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            // 清除本地存储
            localStorage.removeItem('userToken');

            this.showNotification('已退出登录', 'info');

            // 跳转到登录页面
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 1000);

        } catch (error) {
            // 即使退出API失败，也要清除本地token
            localStorage.removeItem('userToken');
            window.location.href = 'login.html';
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        btnText.style.display = 'none';
        btnLoading.style.display = 'flex';
        submitBtn.disabled = true;
    }

    /**
     * 隐藏加载状态
     */
    hideLoading(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        btnText.style.display = 'block';
        btnLoading.style.display = 'none';
        submitBtn.disabled = false;
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 使用主题的通知系统
        if (typeof MainTheme !== 'undefined' && MainTheme.showNotification) {
            MainTheme.showNotification(message, type);
        } else {
            // 简单的fallback通知
            alert(message);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ProfileModule();
});
