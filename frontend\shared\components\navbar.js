/**
 * 统一导航栏组件
 * 创业者社区平台
 */

class NavbarComponent {
    constructor(options = {}) {
        this.currentPage = options.currentPage || '';
        this.isLoggedIn = false;
        this.currentUser = null;
        this.init();
    }

    /**
     * 初始化导航栏
     */
    async init() {
        this.render();
        await this.checkLoginStatus();
        this.bindEvents();
        this.updateActiveState();
    }

    /**
     * 渲染导航栏HTML
     */
    render() {
        const navbarHTML = `
            <nav class="nav">
                <div class="container flex justify-between items-center">
                    <div class="text-2xl font-bold" style="color: var(--white); font-family: var(--font-display);">
                        <a href="${this.getHomePath()}" style="color: inherit; text-decoration: none;">NEXUS</a>
                    </div>
                    <div class="flex gap-8">
                        <a href="${this.getPagePath('index.html')}" class="nav-item" data-page="entrepreneurship">创业社区</a>
                        <a href="${this.getPagePath('profile.html')}" class="nav-item" data-page="profile">我的主页</a>
                        <a href="#" class="nav-item" data-page="discover">发现</a>
                        <a href="#" class="nav-item" data-page="messages">消息</a>
                        <a href="${this.getPagePath('login.html')}" class="nav-item" id="authNavItem">登录</a>
                    </div>
                </div>
            </nav>
        `;

        // 插入到页面顶部
        document.body.insertAdjacentHTML('afterbegin', navbarHTML);
    }



    /**
     * 获取首页路径
     */
    getHomePath() {
        // 根据当前页面位置计算相对路径
        const depth = this.getPageDepth();
        return '../'.repeat(depth) + 'index.html';
    }

    /**
     * 获取页面路径
     */
    getPagePath(filename) {
        const depth = this.getPageDepth();
        
        // 如果是在pages目录下，直接使用文件名
        if (depth === 0) {
            return filename;
        }
        
        // 如果在更深的目录，需要回到pages目录
        return '../'.repeat(depth) + 'pages/' + filename;
    }

    /**
     * 获取当前页面的目录深度
     */
    getPageDepth() {
        const path = window.location.pathname;
        
        // 计算从当前位置到entrepreneurship模块根目录的深度
        if (path.includes('/pages/')) {
            return 0; // 在pages目录下
        } else if (path.includes('/entrepreneurship/')) {
            return 1; // 在entrepreneurship根目录下
        }
        
        return 0; // 默认深度
    }

    /**
     * 检查登录状态
     */
    async checkLoginStatus() {
        const token = localStorage.getItem('userToken');
        if (!token) {
            this.isLoggedIn = false;
            return;
        }

        try {
            const response = await fetch(this.getApiPath('auth/verify.php'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();
            if (result.success) {
                this.isLoggedIn = true;
                this.currentUser = result.data;
                this.updateAuthNavItem();
            } else {
                this.isLoggedIn = false;
                localStorage.removeItem('userToken');
            }
        } catch (error) {
            this.isLoggedIn = false;
            localStorage.removeItem('userToken');
        }
    }

    /**
     * 获取API路径
     */
    getApiPath(endpoint) {
        const depth = this.getPageDepth();
        return '../'.repeat(depth + 1) + 'backend/api/' + endpoint;
    }

    /**
     * 更新认证导航项
     */
    updateAuthNavItem() {
        const authNavItem = document.getElementById('authNavItem');
        if (authNavItem) {
            if (this.isLoggedIn) {
                authNavItem.textContent = '退出';
                authNavItem.href = '#';
            } else {
                authNavItem.textContent = '登录';
                authNavItem.href = this.getPagePath('login.html');
            }
        }
    }

    /**
     * 更新活跃状态
     */
    updateActiveState() {
        // 移除所有活跃状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // 根据当前页面设置活跃状态
        if (this.currentPage) {
            const activeItem = document.querySelector(`[data-page="${this.currentPage}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        const authNavItem = document.getElementById('authNavItem');
        if (authNavItem) {
            authNavItem.addEventListener('click', (e) => {
                if (this.isLoggedIn) {
                    e.preventDefault();
                    this.handleLogout();
                }
            });
        }
    }

    /**
     * 处理退出登录
     */
    async handleLogout() {
        try {
            const token = localStorage.getItem('userToken');
            
            // 调用后端退出API
            await fetch(this.getApiPath('auth/logout.php'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            // 清除本地存储
            localStorage.removeItem('userToken');
            
            this.showNotification('已退出登录', 'info');

            // 立即跳转到创业社区主页
            window.location.href = this.getPagePath('index.html');

        } catch (error) {
            // 即使退出API失败，也要清除本地token
            localStorage.removeItem('userToken');
            window.location.href = this.getPagePath('index.html');
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 使用主题的通知系统
        if (typeof MainTheme !== 'undefined' && MainTheme.showNotification) {
            MainTheme.showNotification(message, type);
        } else {
            // 简单的fallback通知
            alert(message);
        }
    }

    /**
     * 刷新登录状态
     */
    async refreshLoginStatus() {
        await this.checkLoginStatus();
    }

    /**
     * 获取当前用户信息
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 检查是否已登录
     */
    getLoginStatus() {
        return this.isLoggedIn;
    }
}

// 导出供全局使用
window.NavbarComponent = NavbarComponent;
