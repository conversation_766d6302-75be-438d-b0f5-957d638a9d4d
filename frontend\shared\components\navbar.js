/**
 * 统一导航栏组件
 * 创业者社区平台
 */

class NavbarComponent {
    constructor(options = {}) {
        this.currentPage = options.currentPage || '';
        this.pageType = options.pageType || 'entrepreneurship'; // 页面类型：home, entrepreneurship, auth
        this.isLoggedIn = false;
        this.currentUser = null;
        this.init();
    }

    /**
     * 初始化导航栏
     */
    async init() {
        this.render();
        this.bindEvents();
        this.updateActiveState();

        // 延迟检查登录状态，确保DOM完全渲染
        setTimeout(async () => {
            await this.checkLoginStatus();
        }, 100);
    }

    /**
     * 获取导航项配置
     */
    getNavItems() {
        const configs = {
            home: [
                { href: 'frontend/modules/entrepreneurship/pages/index.html', text: '创业', dataPage: 'entrepreneurship', active: this.currentPage === 'entrepreneurship' },
                { href: '#', text: '相亲', dataPage: 'dating' },
                { href: '#', text: '关于', dataPage: 'about' },
                { href: '#', text: '联系', dataPage: 'contact' }
            ],
            entrepreneurship: [
                { href: this.getPagePath('index.html'), text: '创业社区', dataPage: 'entrepreneurship' },
                { href: this.getPagePath('profile.html'), text: '我的主页', dataPage: 'profile' },
                { href: '#', text: '发现', dataPage: 'discover' },
                { href: '#', text: '消息', dataPage: 'messages' },
                { href: this.getPagePath('login.html'), text: '登录', dataPage: 'auth', id: 'authNavItem' }
            ],
            auth: [
                { href: this.getPagePath('index.html'), text: '返回社区', dataPage: 'entrepreneurship' },
                { href: this.getAuthPagePath(), text: this.getAuthPageText(), dataPage: 'auth-switch' }
            ]
        };

        return configs[this.pageType] || configs.entrepreneurship;
    }

    /**
     * 渲染导航栏HTML
     */
    render() {
        const navItems = this.getNavItems();
        const navItemsHTML = navItems.map(item => {
            const activeClass = item.active ? ' active' : '';
            const idAttr = item.id ? ` id="${item.id}"` : '';
            return `<a href="${item.href}" class="nav-item${activeClass}" data-page="${item.dataPage}"${idAttr}>${item.text}</a>`;
        }).join('');

        const navbarHTML = `
            <nav class="nav">
                <div class="container flex justify-between items-center">
                    <div class="text-2xl font-bold" style="color: var(--white); font-family: var(--font-display);">
                        <a href="${this.getHomePath()}" style="color: inherit; text-decoration: none;">NEXUS</a>
                    </div>
                    <div class="flex gap-8">
                        ${navItemsHTML}
                    </div>
                </div>
            </nav>
        `;

        // 插入到页面顶部
        document.body.insertAdjacentHTML('afterbegin', navbarHTML);
    }



    /**
     * 获取认证页面路径
     */
    getAuthPagePath() {
        const currentPath = window.location.pathname;
        if (currentPath.includes('login.html')) {
            return this.getPagePath('register.html');
        } else if (currentPath.includes('register.html')) {
            return this.getPagePath('login.html');
        }
        return this.getPagePath('login.html');
    }

    /**
     * 获取认证页面文本
     */
    getAuthPageText() {
        const currentPath = window.location.pathname;
        if (currentPath.includes('login.html')) {
            return '注册账号';
        } else if (currentPath.includes('register.html')) {
            return '已有账号';
        }
        return '登录';
    }

    /**
     * 获取首页路径
     */
    getHomePath() {
        // 根据当前页面位置计算相对路径
        const depth = this.getPageDepth();
        if (this.pageType === 'home') {
            return '#'; // 首页不需要跳转
        }
        return '../'.repeat(depth) + 'index.html';
    }

    /**
     * 获取页面路径
     */
    getPagePath(filename) {
        const depth = this.getPageDepth();
        
        // 如果是在pages目录下，直接使用文件名
        if (depth === 0) {
            return filename;
        }
        
        // 如果在更深的目录，需要回到pages目录
        return '../'.repeat(depth) + 'pages/' + filename;
    }

    /**
     * 获取当前页面的目录深度
     */
    getPageDepth() {
        const path = window.location.pathname;

        // 根据页面类型计算深度
        if (this.pageType === 'home') {
            return 0; // 首页在根目录
        } else if (path.includes('/pages/')) {
            return 0; // 在pages目录下
        } else if (path.includes('/entrepreneurship/')) {
            return 1; // 在entrepreneurship根目录下
        }

        return 0; // 默认深度
    }

    /**
     * 检查登录状态
     */
    async checkLoginStatus(retryCount = 0) {
        const token = localStorage.getItem('userToken');
        if (!token) {
            this.isLoggedIn = false;
            this.updateAuthNavItem();
            return;
        }

        try {
            const apiPath = this.getApiPath('auth/verify.php');

            const response = await fetch(apiPath, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();
            if (result.success) {
                this.isLoggedIn = true;
                this.currentUser = result.data;
                this.updateAuthNavItem();
            } else {
                this.isLoggedIn = false;
                localStorage.removeItem('userToken');
                this.updateAuthNavItem();
            }
        } catch (error) {
            // 如果是网络错误且重试次数少于3次，则重试
            if (retryCount < 3) {
                setTimeout(() => {
                    this.checkLoginStatus(retryCount + 1);
                }, 1000 * (retryCount + 1)); // 递增延迟
                return;
            }

            this.isLoggedIn = false;
            localStorage.removeItem('userToken');
            this.updateAuthNavItem();
        }
    }

    /**
     * 获取API路径
     */
    getApiPath(endpoint) {
        const depth = this.getPageDepth();

        // 根据页面类型计算正确的API路径
        if (this.pageType === 'home') {
            // 首页在根目录，直接访问backend
            return 'backend/api/' + endpoint;
        } else {
            // 其他页面需要根据深度计算相对路径
            return '../'.repeat(depth + 1) + 'backend/api/' + endpoint;
        }
    }

    /**
     * 静态方法：计算API路径（供其他模块使用）
     */
    static calculateApiPath(endpoint) {
        const path = window.location.pathname;

        // 判断页面类型和深度
        if (path.includes('index.html') && !path.includes('/pages/') && !path.includes('/entrepreneurship/')) {
            // 首页
            return 'backend/api/' + endpoint;
        } else if (path.includes('/pages/')) {
            // 在pages目录下
            return '../../../../backend/api/' + endpoint;
        } else if (path.includes('/entrepreneurship/')) {
            // 在entrepreneurship根目录下
            return '../../../../backend/api/' + endpoint;
        }

        // 默认路径
        return '../../../../backend/api/' + endpoint;
    }

    /**
     * 更新认证导航项
     */
    updateAuthNavItem() {
        const authNavItem = document.getElementById('authNavItem');
        if (authNavItem) {
            if (this.isLoggedIn && this.currentUser) {
                authNavItem.textContent = '退出';
                authNavItem.href = '#';
            } else {
                authNavItem.textContent = '登录';
                authNavItem.href = this.getPagePath('login.html');
            }
        }
    }

    /**
     * 更新活跃状态
     */
    updateActiveState() {
        // 移除所有活跃状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // 根据当前页面设置活跃状态
        if (this.currentPage) {
            const activeItem = document.querySelector(`[data-page="${this.currentPage}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
            }
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        const authNavItem = document.getElementById('authNavItem');
        if (authNavItem) {
            authNavItem.addEventListener('click', (e) => {
                if (this.isLoggedIn) {
                    e.preventDefault();
                    this.handleLogout();
                }
            });
        }
    }

    /**
     * 处理退出登录
     */
    async handleLogout() {
        try {
            const token = localStorage.getItem('userToken');
            
            // 调用后端退出API
            await fetch(this.getApiPath('auth/logout.php'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            // 清除本地存储
            localStorage.removeItem('userToken');
            
            this.showNotification('已退出登录', 'info');

            // 立即跳转到创业社区主页
            window.location.href = this.getPagePath('index.html');

        } catch (error) {
            // 即使退出API失败，也要清除本地token
            localStorage.removeItem('userToken');
            window.location.href = this.getPagePath('index.html');
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 使用主题的通知系统
        if (typeof MainTheme !== 'undefined' && MainTheme.showNotification) {
            MainTheme.showNotification(message, type);
        } else {
            // 简单的fallback通知
            alert(message);
        }
    }

    /**
     * 刷新登录状态
     */
    async refreshLoginStatus() {
        await this.checkLoginStatus();
    }

    /**
     * 获取当前用户信息
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * 检查是否已登录
     */
    getLoginStatus() {
        return this.isLoggedIn;
    }
}

// 导出供全局使用
window.NavbarComponent = NavbarComponent;
