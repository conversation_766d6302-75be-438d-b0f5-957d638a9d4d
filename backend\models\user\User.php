<?php
/**
 * 用户模型类
 * 创业者社区平台
 */

require_once __DIR__ . '/../../utils/Database.php';

class User
{
    private $db;
    private $table = 'cy_users';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 创建新用户
     */
    public function create($userData)
    {
        $sql = "INSERT INTO {$this->table} (
            username, nickname, password, bio, main_business,
            side_business, location, description, contact_type, contact_value
        ) VALUES (
            :username, :nickname, :password, :bio, :main_business,
            :side_business, :location, :description, :contact_type, :contact_value
        )";

        $params = [
            ':username' => $userData['username'],
            ':nickname' => $userData['nickname'],
            ':password' => password_hash($userData['password'], PASSWORD_DEFAULT),
            ':bio' => $userData['bio'] ?? null,
            ':main_business' => $userData['main_business'] ?? null,
            ':side_business' => $userData['side_business'] ?? null,
            ':location' => $userData['location'] ?? null,
            ':description' => $userData['description'] ?? null,
            ':contact_type' => $userData['contact_type'] ?? null,
            ':contact_value' => $userData['contact_value'] ?? null
        ];

        $stmt = $this->db->query($sql, $params);
        return $this->db->getConnection()->lastInsertId();
    }

    /**
     * 根据用户名查找用户
     */
    public function findByUsername($username)
    {
        $sql = "SELECT * FROM {$this->table} WHERE username = :username AND is_active = 1";
        $stmt = $this->db->query($sql, [':username' => $username]);
        return $stmt->fetch();
    }

    /**
     * 根据ID查找用户
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = :id AND is_active = 1";
        $stmt = $this->db->query($sql, [':id' => $id]);
        return $stmt->fetch();
    }

    /**
     * 验证用户密码
     */
    public function verifyPassword($password, $hashedPassword)
    {
        return password_verify($password, $hashedPassword);
    }

    /**
     * 更新最后登录信息
     */
    public function updateLastLogin($userId, $ipAddress)
    {
        $sql = "UPDATE {$this->table} SET 
                last_login_at = NOW(), 
                last_login_ip = :ip_address 
                WHERE id = :user_id";
        
        $params = [
            ':user_id' => $userId,
            ':ip_address' => $ipAddress
        ];

        return $this->db->query($sql, $params);
    }

    /**
     * 检查用户名是否已存在
     */
    public function usernameExists($username)
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE username = :username";
        $stmt = $this->db->query($sql, [':username' => $username]);
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }

    /**
     * 更新用户状态
     */
    public function updateStatus($userId, $status)
    {
        $sql = "UPDATE {$this->table} SET status = :status WHERE id = :user_id";
        $params = [
            ':user_id' => $userId,
            ':status' => $status
        ];
        return $this->db->query($sql, $params);
    }

    /**
     * 获取用户的完整信息（包括标签）
     */
    public function getUserWithTags($userId)
    {
        // 获取用户基本信息
        $user = $this->findById($userId);
        if (!$user) {
            return null;
        }

        // 获取用户标签
        $sql = "SELECT tag_name FROM cy_user_tags WHERE user_id = :user_id";
        $stmt = $this->db->query($sql, [':user_id' => $userId]);
        $tags = $stmt->fetchAll(\PDO::FETCH_COLUMN);

        $user['tags'] = $tags;
        return $user;
    }

    /**
     * 添加用户标签
     */
    public function addUserTag($userId, $tagName)
    {
        $sql = "INSERT INTO cy_user_tags (user_id, tag_name) VALUES (:user_id, :tag_name)";
        $params = [
            ':user_id' => $userId,
            ':tag_name' => $tagName
        ];
        return $this->db->query($sql, $params);
    }

    /**
     * 删除用户标签
     */
    public function removeUserTag($userId, $tagName)
    {
        $sql = "DELETE FROM cy_user_tags WHERE user_id = :user_id AND tag_name = :tag_name";
        $params = [
            ':user_id' => $userId,
            ':tag_name' => $tagName
        ];
        return $this->db->query($sql, $params);
    }

    /**
     * 获取所有活跃用户（用于创业者列表）
     */
    public function getAllActiveUsers($limit = 20, $offset = 0, $currentUserId = null)
    {
        $users = [];

        // 如果有当前用户ID，先获取当前用户信息
        if ($currentUserId) {
            $currentUser = $this->getUserWithTags($currentUserId);
            if ($currentUser) {
                // 确保标签是数组格式
                $currentUser['tags'] = $currentUser['tags'] ? $currentUser['tags'] : [];
                $users[] = $currentUser;
            }

            // 获取其他可见用户（排除当前用户）
            $sql = "SELECT u.*,
                    GROUP_CONCAT(ut.tag_name) as tags
                    FROM {$this->table} u
                    LEFT JOIN cy_user_tags ut ON u.id = ut.user_id
                    WHERE u.is_active = 1 AND u.is_visible = 1 AND u.id != :current_user_id
                    GROUP BY u.id
                    ORDER BY u.created_at DESC
                    LIMIT :limit OFFSET :offset";

            $stmt = $this->db->getConnection()->prepare($sql);
            $stmt->bindValue(':current_user_id', (int)$currentUserId, \PDO::PARAM_INT);
            $stmt->bindValue(':limit', (int)$limit, \PDO::PARAM_INT);
            $stmt->bindValue(':offset', (int)$offset, \PDO::PARAM_INT);
            $stmt->execute();

            $otherUsers = $stmt->fetchAll();

            // 合并其他用户到列表
            $users = array_merge($users, $otherUsers);
        } else {
            // 如果没有当前用户ID，只获取可见用户
            $sql = "SELECT u.*,
                    GROUP_CONCAT(ut.tag_name) as tags
                    FROM {$this->table} u
                    LEFT JOIN cy_user_tags ut ON u.id = ut.user_id
                    WHERE u.is_active = 1 AND u.is_visible = 1
                    GROUP BY u.id
                    ORDER BY u.created_at DESC
                    LIMIT :limit OFFSET :offset";

            $stmt = $this->db->getConnection()->prepare($sql);
            $stmt->bindValue(':limit', (int)$limit, \PDO::PARAM_INT);
            $stmt->bindValue(':offset', (int)$offset, \PDO::PARAM_INT);
            $stmt->execute();

            $users = $stmt->fetchAll();
        }

        // 处理标签字符串为数组
        foreach ($users as &$user) {
            if (is_string($user['tags'])) {
                $user['tags'] = $user['tags'] ? explode(',', $user['tags']) : [];
            }
        }

        return $users;
    }

    /**
     * 切换用户可见性
     */
    public function toggleVisibility($userId)
    {
        $sql = "UPDATE {$this->table} SET is_visible = 1 - is_visible WHERE id = :user_id";
        $stmt = $this->db->query($sql, [':user_id' => $userId]);

        // 获取更新后的状态
        $user = $this->findById($userId);
        return $user ? $user['is_visible'] : null;
    }

    /**
     * 获取用户可见性状态
     */
    public function getVisibility($userId)
    {
        $user = $this->findById($userId);
        return $user ? $user['is_visible'] : null;
    }

    /**
     * 更新用户信息
     */
    public function updateUser($userId, $userData)
    {
        $fields = [];
        $params = [':user_id' => $userId];

        if (isset($userData['nickname'])) {
            $fields[] = 'nickname = :nickname';
            $params[':nickname'] = $userData['nickname'];
        }
        if (isset($userData['bio'])) {
            $fields[] = 'bio = :bio';
            $params[':bio'] = $userData['bio'];
        }
        if (isset($userData['main_business'])) {
            $fields[] = 'main_business = :main_business';
            $params[':main_business'] = $userData['main_business'];
        }
        if (isset($userData['side_business'])) {
            $fields[] = 'side_business = :side_business';
            $params[':side_business'] = $userData['side_business'];
        }
        if (isset($userData['contact_type'])) {
            $fields[] = 'contact_type = :contact_type';
            $params[':contact_type'] = $userData['contact_type'];
        }
        if (isset($userData['contact_value'])) {
            $fields[] = 'contact_value = :contact_value';
            $params[':contact_value'] = $userData['contact_value'];
        }
        if (isset($userData['location'])) {
            $fields[] = 'location = :location';
            $params[':location'] = $userData['location'];
        }

        if (empty($fields)) {
            return false;
        }

        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE id = :user_id";
        return $this->db->query($sql, $params);
    }

    /**
     * 关注用户
     */
    public function followUser($followerId, $followingId)
    {
        try {
            // 检查是否已经关注
            $sql = "SELECT COUNT(*) as count FROM cy_user_follows WHERE follower_id = :follower_id AND following_id = :following_id";
            $stmt = $this->db->query($sql, [
                ':follower_id' => $followerId,
                ':following_id' => $followingId
            ]);
            $result = $stmt->fetch();

            if ($result['count'] > 0) {
                return false; // 已经关注过了
            }

            // 添加关注关系
            $sql = "INSERT INTO cy_user_follows (follower_id, following_id) VALUES (:follower_id, :following_id)";
            $this->db->query($sql, [
                ':follower_id' => $followerId,
                ':following_id' => $followingId
            ]);

            // 更新关注者的关注数
            $sql = "UPDATE {$this->table} SET following_count = following_count + 1 WHERE id = :follower_id";
            $this->db->query($sql, [':follower_id' => $followerId]);

            // 更新被关注者的粉丝数
            $sql = "UPDATE {$this->table} SET followers_count = followers_count + 1 WHERE id = :following_id";
            $this->db->query($sql, [':following_id' => $followingId]);

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 取消关注用户
     */
    public function unfollowUser($followerId, $followingId)
    {
        try {
            // 删除关注关系
            $sql = "DELETE FROM cy_user_follows WHERE follower_id = :follower_id AND following_id = :following_id";
            $stmt = $this->db->query($sql, [
                ':follower_id' => $followerId,
                ':following_id' => $followingId
            ]);

            if ($stmt->rowCount() > 0) {
                // 更新关注者的关注数
                $sql = "UPDATE {$this->table} SET following_count = GREATEST(0, following_count - 1) WHERE id = :follower_id";
                $this->db->query($sql, [':follower_id' => $followerId]);

                // 更新被关注者的粉丝数
                $sql = "UPDATE {$this->table} SET followers_count = GREATEST(0, followers_count - 1) WHERE id = :following_id";
                $this->db->query($sql, [':following_id' => $followingId]);

                return true;
            }

            return false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 检查是否关注了某个用户
     */
    public function isFollowing($followerId, $followingId)
    {
        $sql = "SELECT COUNT(*) as count FROM cy_user_follows WHERE follower_id = :follower_id AND following_id = :following_id";
        $stmt = $this->db->query($sql, [
            ':follower_id' => $followerId,
            ':following_id' => $followingId
        ]);
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }
}
