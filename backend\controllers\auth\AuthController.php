<?php
/**
 * 认证控制器
 * 创业者社区平台
 */

require_once __DIR__ . '/../../models/user/User.php';

class AuthController
{
    private $userModel;

    public function __construct()
    {
        $this->userModel = new User();
    }

    /**
     * 用户注册
     */
    public function register()
    {
        try {
            // 获取POST数据
            $rawInput = file_get_contents('php://input');
            $input = json_decode($rawInput, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('无效的JSON数据');
            }

            if (!$input) {
                $input = $_POST;
            }

            // 验证必填字段
            $requiredFields = ['username', 'nickname', 'password', 'confirmPassword'];
            foreach ($requiredFields as $field) {
                if (empty($input[$field])) {
                    throw new Exception("字段 {$field} 不能为空");
                }
            }

            // 验证密码
            if ($input['password'] !== $input['confirmPassword']) {
                throw new Exception('两次输入的密码不一致');
            }

            if (strlen($input['password']) < 6) {
                throw new Exception('密码至少需要6位字符');
            }

            // 验证用户名格式
            if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $input['username'])) {
                throw new Exception('用户名只能包含字母、数字和下划线，长度3-20位');
            }

            // 检查用户名是否已存在
            if ($this->userModel->usernameExists($input['username'])) {
                throw new Exception('用户名已存在');
            }

            // 验证联系方式
            if (!empty($input['contactType']) && empty($input['contactValue'])) {
                throw new Exception('选择了联系方式类型后必须填写联系方式');
            }

            if (!empty($input['contactType']) && !in_array($input['contactType'], ['phone', 'qq', 'wechat'])) {
                throw new Exception('无效的联系方式类型');
            }

            // 验证手机号格式
            if ($input['contactType'] === 'phone' && !empty($input['contactValue'])) {
                if (!preg_match('/^1[3-9]\d{9}$/', $input['contactValue'])) {
                    throw new Exception('请输入正确的手机号码');
                }
            }

            // 准备用户数据
            $userData = [
                'username' => trim($input['username']),
                'nickname' => trim($input['nickname']),
                'password' => $input['password'],
                'bio' => !empty($input['bio']) ? trim($input['bio']) : null,
                'main_business' => !empty($input['mainBusiness']) ? trim($input['mainBusiness']) : null,
                'side_business' => !empty($input['sideBusiness']) ? trim($input['sideBusiness']) : null,
                'location' => !empty($input['location']) ? trim($input['location']) : null,
                'contact_type' => !empty($input['contactType']) ? $input['contactType'] : null,
                'contact_value' => !empty($input['contactValue']) ? trim($input['contactValue']) : null
            ];

            // 创建用户
            $userId = $this->userModel->create($userData);

            if (!$userId) {
                throw new Exception('用户创建失败');
            }

            // 返回成功响应
            echo json_encode([
                'success' => true,
                'message' => '注册成功',
                'data' => [
                    'user_id' => $userId,
                    'username' => $userData['username'],
                    'nickname' => $userData['nickname']
                ]
            ]);

        } catch (Exception $e) {
            $errorMessage = $e->getMessage();

            // 检查是否是数据库表不存在的错误
            if (strpos($errorMessage, "Table") !== false && strpos($errorMessage, "doesn't exist") !== false) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => $errorMessage,
                    'error_type' => 'table_not_exists'
                ]);
            } else {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $errorMessage
                ]);
            }
        }
    }

    /**
     * 用户登录
     */
    public function login()
    {
        try {
            // 获取POST数据
            $rawInput = file_get_contents('php://input');
            $input = json_decode($rawInput, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('无效的JSON数据');
            }

            if (!$input) {
                $input = $_POST;
            }

            // 验证必填字段
            if (empty($input['username']) || empty($input['password'])) {
                throw new Exception('用户名和密码不能为空');
            }

            // 查找用户
            $user = $this->userModel->findByUsername($input['username']);
            if (!$user) {
                throw new Exception('用户名或密码错误');
            }

            // 验证密码
            if (!$this->userModel->verifyPassword($input['password'], $user['password'])) {
                throw new Exception('用户名或密码错误');
            }

            // 更新最后登录信息
            $ipAddress = $this->getClientIP();
            $this->userModel->updateLastLogin($user['id'], $ipAddress);

            // 生成会话令牌
            $token = $this->generateToken($user['id']);

            // 如果选择了记住我，设置更长的过期时间
            $rememberMe = !empty($input['rememberMe']);
            $expireTime = $rememberMe ? time() + (30 * 24 * 60 * 60) : time() + (24 * 60 * 60); // 30天或1天

            // 设置会话
            $this->setUserSession($user['id'], $token, $expireTime);

            // 返回成功响应
            echo json_encode([
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'nickname' => $user['nickname'],
                    'token' => $token,
                    'expires_at' => date('Y-m-d H:i:s', $expireTime)
                ]
            ]);

        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 用户退出登录
     */
    public function logout()
    {
        try {
            // 清除会话
            session_start();
            session_destroy();

            echo json_encode([
                'success' => true,
                'message' => '退出登录成功'
            ]);

        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 验证用户令牌
     */
    public function verifyToken()
    {
        try {
            $token = $this->getTokenFromRequest();
            if (!$token) {
                throw new Exception('未提供令牌');
            }

            $userId = $this->validateToken($token);
            if (!$userId) {
                throw new Exception('无效的令牌');
            }

            $user = $this->userModel->findById($userId);
            if (!$user) {
                throw new Exception('用户不存在');
            }

            echo json_encode([
                'success' => true,
                'message' => '令牌有效',
                'data' => [
                    'user_id' => $user['id'],
                    'username' => $user['username'],
                    'nickname' => $user['nickname']
                ]
            ]);

        } catch (Exception $e) {
            http_response_code(401);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 生成用户令牌
     */
    private function generateToken($userId)
    {
        $payload = [
            'user_id' => $userId,
            'timestamp' => time(),
            'random' => bin2hex(random_bytes(16))
        ];
        
        return base64_encode(json_encode($payload));
    }

    /**
     * 验证令牌
     */
    private function validateToken($token)
    {
        try {
            $payload = json_decode(base64_decode($token), true);
            if (!$payload || !isset($payload['user_id'])) {
                return false;
            }

            // 检查令牌是否过期（24小时）
            if (time() - $payload['timestamp'] > 24 * 60 * 60) {
                return false;
            }

            return $payload['user_id'];
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 从请求中获取令牌
     */
    private function getTokenFromRequest()
    {
        // 从Authorization头获取
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            return str_replace('Bearer ', '', $headers['Authorization']);
        }

        // 从POST数据获取
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input && isset($input['token'])) {
            return $input['token'];
        }

        // 从GET参数获取
        if (isset($_GET['token'])) {
            return $_GET['token'];
        }

        return null;
    }

    /**
     * 设置用户会话
     */
    private function setUserSession($userId, $token, $expireTime)
    {
        session_start();
        $_SESSION['user_id'] = $userId;
        $_SESSION['token'] = $token;
        $_SESSION['expires_at'] = $expireTime;
    }

    /**
     * 获取客户端IP地址
     */
    private function getClientIP()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                return trim($ip);
            }
        }
        
        return '0.0.0.0';
    }
}
