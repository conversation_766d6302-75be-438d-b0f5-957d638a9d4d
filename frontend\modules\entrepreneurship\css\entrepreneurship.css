/* 创业模块专用样式 */

/* 功能导航样式 */
.function-nav {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.nav-tabs {
    display: flex;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 16px;
    padding: 8px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
}

.tab-btn {
    padding: 12px 24px;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    border-radius: 12px;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out-expo);
    font-size: var(--font-size-base);
    white-space: nowrap;
}

.tab-btn:hover {
    color: var(--white);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: var(--white);
    box-shadow: var(--shadow-md), 0 0 20px rgba(99, 102, 241, 0.4);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 创业者网格布局 */
.entrepreneurs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
    margin-bottom: 2rem;
}

/* 创业者卡片样式 */
.entrepreneur-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    transition: all var(--duration-normal) var(--ease-out-expo);
    box-shadow: var(--shadow-lg);
}

.entrepreneur-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl), 0 0 40px rgba(99, 102, 241, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

.entrepreneur-card .card-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 0;
    border: none;
    background: none;
}

.user-info {
    flex: 1;
}

.user-info h3 {
    color: var(--white);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: 4px;
}

.user-info .title {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    margin-bottom: 8px;
}

.user-info .location {
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
}

.follow-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: var(--white);
    border: none;
    border-radius: 8px;
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out-expo);
    box-shadow: var(--shadow-sm);
}

.follow-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md), 0 0 15px rgba(99, 102, 241, 0.4);
}

.follow-btn.followed {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.business-info {
    margin-bottom: 16px;
}

.business-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.business-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.business-item .label {
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.business-item .value {
    color: var(--white);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* 联系方式样式 */
.contact-info.hidden {
    color: rgba(255, 255, 255, 0.3);
    font-family: monospace;
    letter-spacing: 1px;
    user-select: none;
    cursor: help;
    font-size: var(--font-size-xs);
    opacity: 0.8;
    position: relative;
    transition: all var(--duration-normal);
}

.contact-info.hidden:hover {
    color: rgba(255, 255, 255, 0.5);
    opacity: 1;
}

.contact-info.hidden::after {
    content: '关注后可查看联系方式';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 6px;
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--duration-normal);
    z-index: 10;
    font-family: var(--font-family);
    letter-spacing: normal;
}

.contact-info.hidden:hover::after {
    opacity: 1;
}

.contact-info.visible {
    color: #10b981;
    font-weight: 600;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05));
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(16, 185, 129, 0.2);
    cursor: pointer;
    transition: all var(--duration-normal);
    position: relative;
}

.contact-info.visible:hover {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
    border-color: rgba(16, 185, 129, 0.4);
    transform: scale(1.02);
}

.contact-info.visible::after {
    content: '点击复制';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 6px;
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--duration-normal);
    z-index: 10;
    font-weight: normal;
}

.contact-info.visible:hover::after {
    opacity: 1;
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.tag {
    padding: 4px 12px;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
    color: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: 1px solid rgba(99, 102, 241, 0.3);
}

.entrepreneur-card .card-footer {
    padding: 0;
    border: none;
    background: none;
}

.stats {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-xs);
}

/* 帖子样式 */
.posts-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.post-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    transition: all var(--duration-normal) var(--ease-out-expo);
    box-shadow: var(--shadow-md);
}

.post-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg), 0 0 30px rgba(99, 102, 241, 0.15);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.author-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.author-info h4 {
    color: var(--white);
    font-size: var(--font-size-base);
    font-weight: 600;
    margin-bottom: 2px;
}

.post-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
}

.post-category {
    padding: 4px 12px;
    background: linear-gradient(135deg, #ec4899, #f59e0b);
    color: var(--white);
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.post-content h3 {
    color: var(--white);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: 12px;
}

.post-content p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 16px;
}

.post-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 16px;
}

.post-stats {
    display: flex;
    gap: 24px;
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
}

.post-stats span {
    cursor: pointer;
    transition: color var(--duration-fast);
}

.post-stats span:hover {
    color: var(--white);
}

/* 资源合作样式 */
.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.resource-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 16px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    transition: all var(--duration-normal) var(--ease-out-expo);
    box-shadow: var(--shadow-md);
}

.resource-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.resource-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.resource-type {
    padding: 4px 12px;
    background: linear-gradient(135deg, #10b981, #0ea5e9);
    color: var(--white);
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.urgency {
    padding: 4px 8px;
    border-radius: 8px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.urgency.high {
    background: #dc2626;
    color: var(--white);
}

.resource-card h3 {
    color: var(--white);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: 12px;
}

.resource-card p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 16px;
}

.resource-footer {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.6);
    font-size: var(--font-size-sm);
}

/* 弹幕样式 */
.danmaku-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: var(--shadow-lg);
}

.danmaku-display {
    height: 200px;
    position: relative;
    overflow: hidden;
    margin-bottom: 20px;
    border-radius: 12px;
    background: rgba(0, 0, 0, 0.2);
}

.danmaku-item {
    position: absolute;
    color: var(--white);
    font-size: var(--font-size-base);
    font-weight: 500;
    white-space: nowrap;
    animation: danmakuMove 8s linear infinite;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.danmaku-item:nth-child(1) {
    top: 20%;
    animation-delay: 0s;
}

.danmaku-item:nth-child(2) {
    top: 50%;
    animation-delay: 2s;
}

.danmaku-item:nth-child(3) {
    top: 80%;
    animation-delay: 4s;
}

@keyframes danmakuMove {
    from {
        right: -100%;
    }
    to {
        right: 100%;
    }
}

.danmaku-input {
    display: flex;
    gap: 12px;
}

.danmaku-text {
    flex: 1;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: var(--white);
    font-size: var(--font-size-base);
    outline: none;
    transition: all var(--duration-normal);
}

.danmaku-text::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.danmaku-text:focus {
    border-color: rgba(99, 102, 241, 0.5);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.send-danmaku-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: var(--white);
    border: none;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out-expo);
    box-shadow: var(--shadow-md);
}

.send-danmaku-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(99, 102, 241, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .entrepreneurs-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-tabs {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .tab-btn {
        padding: 8px 16px;
        font-size: var(--font-size-sm);
    }
    
    .entrepreneur-card .card-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .resources-grid {
        grid-template-columns: 1fr;
    }
}
